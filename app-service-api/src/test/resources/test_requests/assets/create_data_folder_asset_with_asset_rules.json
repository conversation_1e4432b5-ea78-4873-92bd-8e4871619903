{"dataSourceId": "${dataSourceId}", "name": "Test CSV Asset", "description": "Test asset description", "dataFolderSettings": {"_type": "DataFolderAssetSettingsDto", "baseFolder": "/test/folder", "baseInstant": null, "fileNameFilter": {"_type": "RegexFileNameFilterDto", "pattern": "data_.*\\.csv"}, "compressed": false}, "dataFormat": "CSV", "dataFormatSpecificSettings": {"_type": "CsvDataFormatSettingsDto", "delimiter": ",", "header": true, "quote": "\"", "escape": "\\"}, "schema": {"_type": "CsvSchemaDto", "columns": [{"name": "id", "type": "NUMBER"}, {"name": "name", "type": "TEXT"}]}, "rules": [{"ruleName": "Freshness Check", "ruleType": "ASSET_FRESHNESS", "validationRule": {"_type": "AssetFreshnessValidationRuleDto", "maxLagMinutes": 1440}, "executionCron": "0 */15 * * * ?", "active": true}, {"ruleName": "Volume Check", "ruleType": "ASSET_VOLUME", "validationRule": {"_type": "AssetVolumeValidationRuleDto", "durationUnit": "HOURS", "durationAmount": 1, "minRecords": 100, "maxRecords": 1000}, "executionCron": "0 0 * * * ?", "active": true}]}