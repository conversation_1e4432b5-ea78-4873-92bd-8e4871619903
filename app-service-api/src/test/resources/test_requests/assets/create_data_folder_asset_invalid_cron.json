{"dataSourceId": "${dataSourceId}", "name": "Test CSV Asset", "description": "Test asset description", "dataFolderSettings": {"_type": "DataFolderAssetSettingsDto", "baseFolder": "/test/folder", "baseInstant": null, "fileNameFilter": {"_type": "RegexFileNameFilterDto", "pattern": "data_.*\\.csv"}, "compressed": false}, "dataFormat": "CSV", "dataFormatSpecificSettings": {"_type": "CsvDataFormatSettingsDto", "delimiter": ",", "header": true, "quote": "\"", "escape": "\\"}, "schema": {"_type": "CsvSchemaDto", "columns": [{"name": "id", "type": "NUMBER"}]}, "rules": [{"ruleName": "Rule with invalid cron", "ruleType": "ASSET_FRESHNESS", "validationRule": {"_type": "AssetFreshnessValidationRuleDto", "maxLagMinutes": 1440}, "executionCron": "invalid cron", "active": true}]}