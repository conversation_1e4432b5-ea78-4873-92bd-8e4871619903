{"dataSourceId": "${dataSourceId}", "name": "Test Kafka Topic Asset", "description": "Test Kafka topic asset description", "kafkaTopicSettings": {"_type": "KafkaTopicAssetSettingsDto", "topicName": "test-topic", "maxItemsForChunk": 5000, "chunkGenerationTimeoutSeconds": 1800}, "dataFormat": "JSON", "dataFormatSpecificSettings": {"_type": "JsonDataFormatSettingsDto", "dateFormat": "yyyy-MM-dd'T'HH:mm:ss.SSSZ", "ignoreUnknownProperties": true}, "schema": {"_type": "JsonSchemaDto", "schema": {"type": "object", "properties": {"id": {"type": "integer"}, "name": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}, "data": {"type": "object", "properties": {"value": {"type": "number"}, "tags": {"type": "array", "items": {"type": "string"}}}}}, "required": ["id", "timestamp"]}}, "rules": []}