package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.dashboard.RuleExecutionMetricsResponseDto
import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcessId
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.models.RuleSetExecution
import com.spars.dens.core.datamodel.rule.models.RuleSetExecutionResultDetails
import com.spars.dens.core.datamodel.rule.models.SingleRuleExecutionResult
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.rule.repositories.RuleSetExecutionRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import kotlinx.datetime.Clock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours

class DashboardDistinctRulesTestedControllerTest : BaseControllerTest() {

    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    lateinit var assetRepository: AssetRepository

    @Autowired
    lateinit var ruleRepository: RuleRepository

    @Autowired
    lateinit var ruleSetExecutionRepository: RuleSetExecutionRepository

    private val dataSourceFixture = DataSourceFixture()
    private val tenantFixture = TenantFixture()
    private val assetFixture = AssetFixture()
    private val fieldRuleFixture1 = FieldRuleFixture(ruleId = RuleId.from(1234567890L))
    private val fieldRuleFixture2 = FieldRuleFixture(ruleId = RuleId.from(2345678901L))
    private val fieldRuleFixture3 = FieldRuleFixture(ruleId = RuleId.from(3456789012L))

    @BeforeEach
    override fun setup() {
        super.setup()
        dataSourceRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )
        assetRepository.create(tenantFixture.tenantId, dslContext, assetFixture.createData)
        ruleRepository.create(
            tenantFixture.tenantId, dslContext,
            listOf(fieldRuleFixture1.createData, fieldRuleFixture2.createData, fieldRuleFixture3.createData)
        )
    }

    @Test
    @WithMockUser
    fun `test distinct rules tested with multiple rules in current period`() {
        // Setup - create rule executions with different rules in current period
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Create result details with rule execution results
        val resultDetails1 = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture1.ruleId,
                    numRowsScanned = 1000,
                    numRowsPassed = 900,
                    numRowsFailed = 100,
                    sampleFailingValues = emptyList()
                ),
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture2.ruleId,
                    numRowsScanned = 1000,
                    numRowsPassed = 950,
                    numRowsFailed = 50,
                    sampleFailingValues = emptyList()
                )
            )
        )

        val resultDetails2 = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture2.ruleId,
                    numRowsScanned = 500,
                    numRowsPassed = 450,
                    numRowsFailed = 50,
                    sampleFailingValues = emptyList()
                ),
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture3.ruleId,
                    numRowsScanned = 500,
                    numRowsPassed = 480,
                    numRowsFailed = 20,
                    sampleFailingValues = emptyList()
                )
            )
        )

        val execution1 = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture1.ruleId, fieldRuleFixture2.ruleId),
            plannedStartTime = now.minus(13.hours),
            startTime = now.minus(13.hours),
            endTime = now.minus(12.hours),
            numRowsScanned = 1000,
            numRowsPassingAllRules = 900,
            numRowsFailingAnyRule = 100,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = resultDetails1,
            createdAt = now.minus(12.hours)
        )

        val execution2 = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture2.ruleId, fieldRuleFixture3.ruleId),
            plannedStartTime = now.minus(7.hours),
            startTime = now.minus(7.hours),
            endTime = now.minus(6.hours),
            numRowsScanned = 500,
            numRowsPassingAllRules = 450,
            numRowsFailingAnyRule = 50,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = resultDetails2,
            createdAt = now.minus(6.hours)
        )

        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution1)
        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution2)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/distinct-rules-tested")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(3.0) // 3 distinct rules
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(0.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(100.0)
    }

    @Test
    @WithMockUser
    fun `test distinct rules tested with data in both periods`() {
        // Setup - create rule executions in current and previous periods
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Create result details with rule execution results
        val currentPeriodResultDetails = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture1.ruleId,
                    numRowsScanned = 1000,
                    numRowsPassed = 900,
                    numRowsFailed = 100,
                    sampleFailingValues = emptyList()
                ),
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture2.ruleId,
                    numRowsScanned = 1000,
                    numRowsPassed = 950,
                    numRowsFailed = 50,
                    sampleFailingValues = emptyList()
                )
            )
        )

        val previousPeriodResultDetails = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture1.ruleId,
                    numRowsScanned = 500,
                    numRowsPassed = 450,
                    numRowsFailed = 50,
                    sampleFailingValues = emptyList()
                )
            )
        )

        // Current period executions
        val execution1 = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture1.ruleId, fieldRuleFixture2.ruleId),
            plannedStartTime = now.minus(13.hours),
            startTime = now.minus(13.hours),
            endTime = now.minus(12.hours),
            numRowsScanned = 1000,
            numRowsPassingAllRules = 900,
            numRowsFailingAnyRule = 100,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = currentPeriodResultDetails,
            createdAt = now.minus(12.hours)
        )

        // Previous period executions
        val execution2 = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture1.ruleId),
            plannedStartTime = startTime.minus(13.hours),
            startTime = startTime.minus(13.hours),
            endTime = startTime.minus(12.hours),
            numRowsScanned = 500,
            numRowsPassingAllRules = 450,
            numRowsFailingAnyRule = 50,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = previousPeriodResultDetails,
            createdAt = startTime.minus(12.hours)
        )

        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution1)
        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution2)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/distinct-rules-tested")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(2.0) // 2 distinct rules in current period
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(1.0) // 1 distinct rule in previous period
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(100.0)
    }

    @Test
    @WithMockUser
    fun `test distinct rules tested with no rules in current period`() {
        // Setup - create rule executions only in previous period
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Previous period result details
        val previousPeriodResultDetails = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture1.ruleId,
                    numRowsScanned = 500,
                    numRowsPassed = 450,
                    numRowsFailed = 50,
                    sampleFailingValues = emptyList()
                ),
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture2.ruleId,
                    numRowsScanned = 500,
                    numRowsPassed = 480,
                    numRowsFailed = 20,
                    sampleFailingValues = emptyList()
                )
            )
        )

        // Previous period executions
        val execution = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture1.ruleId, fieldRuleFixture2.ruleId),
            plannedStartTime = startTime.minus(13.hours),
            startTime = startTime.minus(13.hours),
            endTime = startTime.minus(12.hours),
            numRowsScanned = 500,
            numRowsPassingAllRules = 450,
            numRowsFailingAnyRule = 50,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = previousPeriodResultDetails,
            createdAt = startTime.minus(12.hours)
        )

        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/distinct-rules-tested")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(0.0)
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(2.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(-100.0)
    }

    @Test
    @WithMockUser
    fun `test distinct rules tested with duplicate rule executions`() {
        // Setup - create multiple rule executions with the same rules
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Create result details with the same rule execution results
        val resultDetails1 = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture1.ruleId,
                    numRowsScanned = 1000,
                    numRowsPassed = 900,
                    numRowsFailed = 100,
                    sampleFailingValues = emptyList()
                )
            )
        )

        val resultDetails2 = RuleSetExecutionResultDetails(
            dataChunkId = AssetDataChunkInProcessId.from(9999999L),
            singleRuleExecutionResults = listOf(
                SingleRuleExecutionResult(
                    ruleId = fieldRuleFixture1.ruleId,
                    numRowsScanned = 500,
                    numRowsPassed = 450,
                    numRowsFailed = 50,
                    sampleFailingValues = emptyList()
                )
            )
        )

        // Multiple executions with the same rule
        val execution1 = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture1.ruleId),
            plannedStartTime = now.minus(13.hours),
            startTime = now.minus(13.hours),
            endTime = now.minus(12.hours),
            numRowsScanned = 1000,
            numRowsPassingAllRules = 900,
            numRowsFailingAnyRule = 100,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = resultDetails1,
            createdAt = now.minus(12.hours)
        )

        val execution2 = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture1.ruleId),
            plannedStartTime = now.minus(7.hours),
            startTime = now.minus(7.hours),
            endTime = now.minus(6.hours),
            numRowsScanned = 500,
            numRowsPassingAllRules = 450,
            numRowsFailingAnyRule = 50,
            numRowsSkipped = 0,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = 90.0,
            resultDetails = resultDetails2,
            createdAt = now.minus(6.hours)
        )

        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution1)
        ruleSetExecutionRepository.create(tenantFixture.tenantId, dslContext, execution2)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/distinct-rules-tested")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(1.0) // Only 1 distinct rule
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(0.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(100.0)
    }
}
