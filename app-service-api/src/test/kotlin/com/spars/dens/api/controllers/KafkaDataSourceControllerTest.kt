package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.ErrorCode
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSourceId
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.models.KafkaSecurityProtocol
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.folder.models.FileSystemProtocol
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import io.confluent.kafka.schemaregistry.client.CachedSchemaRegistryClient
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import org.apache.kafka.clients.admin.AdminClient
import org.apache.kafka.clients.admin.CreateTopicsOptions
import org.apache.kafka.clients.admin.DeleteTopicsOptions
import org.apache.kafka.clients.admin.NewTopic
import org.apache.kafka.common.errors.TopicExistsException
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import java.util.Properties
import java.util.concurrent.TimeUnit

class KafkaDataSourceControllerTest : BaseControllerTest() {

    private val testTenantId = TenantFixture().tenantId

    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    // Test data
    private val testTopicsWithSchema = listOf("test-topic-with-schema", "orders-topic")
    private val testTopicsWithoutSchema = listOf("test-topic-no-schema", "logs-topic")
    private val testTopicsWithPrefix = listOf("user-events", "user-analytics", "system-logs")

    private lateinit var kafkaDataSource: com.spars.dens.core.datamodel.datasource.models.DataSource
    private lateinit var nonKafkaDataSource: com.spars.dens.core.datamodel.datasource.models.DataSource

    // Kafka and Schema Registry clients for test setup
    private lateinit var adminClient: AdminClient
    private lateinit var schemaRegistryClient: SchemaRegistryClient

    @BeforeEach
    override fun setup() {
        super.setup()

        // Initialize Kafka AdminClient
        adminClient = createKafkaAdminClient()

        // Initialize Schema Registry client
        schemaRegistryClient = createSchemaRegistryClient()

        // Clean up any pre-existing test topics and schemas first
        cleanupPreExistingTestData()

        // Create test data sources
        setupTestDataSources()

        // Create test topics and schemas
        setupTestTopicsAndSchemas()
    }

    @AfterEach
    fun cleanup() {
        try {
            // Clean up topics
            cleanupTestTopics()

            // Clean up schemas
            cleanupTestSchemas()

            // Close clients
            adminClient.close()
        } catch (e: Exception) {
            println("Error during cleanup: ${e.message}")
        }
    }

    private fun createKafkaAdminClient(): AdminClient {
        val properties = Properties().apply {
            put("bootstrap.servers", "localhost:9092")
            put("client.id", "test-admin-client")
            put("request.timeout.ms", "20000")
            put("connections.max.idle.ms", "10000")
            put("retry.backoff.ms", "500")
        }
        return AdminClient.create(properties)
    }

    private fun createSchemaRegistryClient(): SchemaRegistryClient {
        return CachedSchemaRegistryClient("http://localhost:8081", 100)
    }

    private fun setupTestDataSources() {
        // Create Kafka data source
        val kafkaDataSourceFixture = DataSourceFixture(
            tenantId = testTenantId,
            dataSourceName = "Test Kafka Data Source",
            dataSourceType = DataSourceType.KAFKA,
            connectionSettings = ConnectionSettings.KafkaConnectionSettings(
                bootstrapServers = "localhost:9092",
                applicationId = "test-app",
                securityProtocol = KafkaSecurityProtocol.PLAINTEXT,
                userName = null,
                password = null,
                schemaRegistryUrl = "http://localhost:8081"
            )
        )

        kafkaDataSource = dataSourceRepository.create(
            tenantId = testTenantId,
            dslContext = dslContext,
            createData = kafkaDataSourceFixture.createData
        )

        // Create non-Kafka data source for negative testing
        val folderDataSourceFixture = DataSourceFixture(
            tenantId = testTenantId,
            dataSourceId = DataSourceId.from(123456789012L),
            dataSourceName = "Test Folder Data Source",
            dataSourceType = DataSourceType.FOLDER,
            connectionSettings = ConnectionSettings.FolderConnectionSettings(
                protocol = FileSystemProtocol.SFTP,
                host = "localhost",
                port = 2222,
                userName = "user",
                password = "password"
            )
        )

        nonKafkaDataSource = dataSourceRepository.create(
            tenantId = testTenantId,
            dslContext = dslContext,
            createData = folderDataSourceFixture.createData
        )
    }

    private fun setupTestTopicsAndSchemas() {
        // Create all test topics
        val allTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix
        createKafkaTopics(allTopics)

        // Register schemas for topics that should have schemas
        registerSchemasForTopics(testTopicsWithSchema)
    }

    private fun createKafkaTopics(topicNames: List<String>) {
        val newTopics = topicNames.map { topicName ->
            NewTopic(topicName, 1, 1.toShort())
        }

        try {
            val options = CreateTopicsOptions().timeoutMs(30000)
            adminClient.createTopics(newTopics, options).all().get(30, TimeUnit.SECONDS)
            println("Created topics: $topicNames")
        } catch (e: Exception) {
            if (e.cause !is TopicExistsException) {
                println("Error creating topics: ${e.message}")
            }
        }
    }

    private fun registerSchemasForTopics(topicNames: List<String>) {
        val simpleJsonSchema = """
            {
                "type": "record",
                "name": "TestRecord",
                "fields": [
                    {"name": "id", "type": "string"},
                    {"name": "timestamp", "type": "long"},
                    {"name": "data", "type": "string"}
                ]
            }
        """.trimIndent()

        topicNames.forEach { topicName ->
            try {
                // Register schema for value
                schemaRegistryClient.register(
                    "$topicName-value",
                    io.confluent.kafka.schemaregistry.avro.AvroSchema(simpleJsonSchema)
                )
                println("Registered schema for topic: $topicName")
            } catch (e: Exception) {
                println("Error registering schema for topic $topicName: ${e.message}")
            }
        }
    }

    private fun cleanupTestTopics() {
        try {
            val allTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix
            val options = DeleteTopicsOptions().timeoutMs(30000)
            adminClient.deleteTopics(allTopics, options).all().get(30, TimeUnit.SECONDS)
            println("Deleted topics: $allTopics")
        } catch (e: Exception) {
            println("Error deleting topics: ${e.message}")
        }
    }

    private fun cleanupTestSchemas() {
        try {
            testTopicsWithSchema.forEach { topicName ->
                try {
                    schemaRegistryClient.deleteSubject("$topicName-value")
                    println("Deleted schema for topic: $topicName")
                } catch (e: Exception) {
                    println("Error deleting schema for topic $topicName: ${e.message}")
                }
            }
        } catch (e: Exception) {
            println("Error during schema cleanup: ${e.message}")
        }
    }

    private fun cleanupPreExistingTestData() {
        try {
            // Clean up any pre-existing test topics that might interfere
            val allTestTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix

            // Try to delete topics (ignore errors if they don't exist)
            try {
                val options = DeleteTopicsOptions().timeoutMs(10000)
                adminClient.deleteTopics(allTestTopics, options).all().get(15, TimeUnit.SECONDS)
                println("Cleaned up pre-existing test topics: $allTestTopics")
            } catch (e: Exception) {
                println("Note: Some test topics may not have existed: ${e.message}")
            }

            // Clean up any pre-existing schemas for our test topics
            allTestTopics.forEach { topicName ->
                try {
                    schemaRegistryClient.deleteSubject("$topicName-value")
                    println("Cleaned up pre-existing schema for topic: $topicName")
                } catch (e: Exception) {
                    // Ignore errors - schema may not exist
                }
            }

            // Wait a bit for cleanup to complete
            Thread.sleep(1000)
        } catch (e: Exception) {
            println("Error during pre-existing data cleanup: ${e.message}")
        }
    }

    // ========== SUCCESS SCENARIOS ==========

    @Test
    @WithMockUser
    fun `test list all topics without filtering`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!
        val allExpectedTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix

        // Verify all our test topics are present (there might be other topics too)
        allExpectedTopics.forEach { expectedTopic ->
            assertThat(topics).contains(expectedTopic)
        }

        // Verify topics are sorted
        val filteredTopics = topics.filter { it in allExpectedTopics }
        assertThat(filteredTopics).isEqualTo(filteredTopics.sorted())
    }

    @Test
    @WithMockUser
    fun `test list topics with schema filtering - hasSchema true`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("hasSchema", "true")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should contain topics with schemas that we created
        testTopicsWithSchema.forEach { expectedTopic ->
            assertThat(topics).contains(expectedTopic)
        }

        // Should not contain topics without schemas that we created
        testTopicsWithoutSchema.forEach { unexpectedTopic ->
            assertThat(topics).doesNotContain(unexpectedTopic)
        }

        // testTopicsWithPrefix don't have schemas (that we registered), so they shouldn't be included
        testTopicsWithPrefix.forEach { unexpectedTopic ->
            assertThat(topics).doesNotContain(unexpectedTopic)
        }
    }

    @Test
    @WithMockUser
    fun `test list topics with schema filtering - hasSchema false`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("hasSchema", "false")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should contain topics without schemas that we created
        val topicsWithoutSchemas = testTopicsWithoutSchema + testTopicsWithPrefix
        topicsWithoutSchemas.forEach { expectedTopic ->
            assertThat(topics).contains(expectedTopic)
        }

        // Should not contain topics with schemas that we created
        testTopicsWithSchema.forEach { unexpectedTopic ->
            assertThat(topics).doesNotContain(unexpectedTopic)
        }
    }

    @Test
    @WithMockUser
    fun `test list topics with prefix filtering - case insensitive`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("topicPrefix", "USER")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should contain topics starting with "user" (case insensitive)
        assertThat(topics).contains("user-events")
        assertThat(topics).contains("user-analytics")

        // Should not contain topics not starting with "user"
        assertThat(topics).doesNotContain("system-logs")
        testTopicsWithSchema.forEach { topic ->
            assertThat(topics).doesNotContain(topic)
        }
        testTopicsWithoutSchema.forEach { topic ->
            assertThat(topics).doesNotContain(topic)
        }
    }

    @Test
    @WithMockUser
    fun `test list topics with prefix filtering - exact match`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("topicPrefix", "test-topic-with-schema")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should contain only the exact match
        assertThat(topics).contains("test-topic-with-schema")

        // Should not contain other topics
        assertThat(topics).doesNotContain("orders-topic")
        assertThat(topics).doesNotContain("test-topic-no-schema")
    }

    @Test
    @WithMockUser
    fun `test list topics with combined filtering - hasSchema true and prefix`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("hasSchema", "true")
                .param("topicPrefix", "test")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should contain only topics with schema AND starting with "test"
        assertThat(topics).contains("test-topic-with-schema")

        // Should not contain topics without schema even if they match prefix
        assertThat(topics).doesNotContain("test-topic-no-schema")

        // Should not contain topics with schema but different prefix
        assertThat(topics).doesNotContain("orders-topic")
    }

    @Test
    @WithMockUser
    fun `test list topics with combined filtering - hasSchema false and prefix`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("hasSchema", "false")
                .param("topicPrefix", "user")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should contain topics without schema AND starting with "user" that we created
        assertThat(topics).contains("user-events")
        assertThat(topics).contains("user-analytics")

        // Should not contain topics with schema that we created
        testTopicsWithSchema.forEach { topic ->
            assertThat(topics).doesNotContain(topic)
        }

        // Should not contain topics without schema but different prefix that we created
        assertThat(topics).doesNotContain("test-topic-no-schema")
        assertThat(topics).doesNotContain("logs-topic")
        assertThat(topics).doesNotContain("system-logs")
    }

    @Test
    @WithMockUser
    fun `test list topics with empty prefix returns all topics`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("topicPrefix", "")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!
        val allExpectedTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix

        // Empty prefix should return all topics (same as no prefix)
        allExpectedTopics.forEach { expectedTopic ->
            assertThat(topics).contains(expectedTopic)
        }
    }

    @Test
    @WithMockUser
    fun `test list topics with non-matching prefix returns empty list`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("topicPrefix", "non-existent-prefix")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!
        val allTestTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix

        // Should not contain any of our test topics
        allTestTopics.forEach { topic ->
            assertThat(topics).doesNotContain(topic)
        }
    }

    // ========== ERROR SCENARIOS ==========

    @Test
    @WithMockUser
    fun `test list topics with non-existent data source returns 404`() {
        val nonExistentDataSourceId = 99999L

        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/$nonExistentDataSourceId/topics")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.NOT_FOUND.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNull()
        assertThat(responseBody.error).isNotNull
        assertThat(responseBody.error!!.code).isEqualTo(ErrorCode.NOT_FOUND)
        assertThat(responseBody.error!!.message).isEqualTo("Data source not found")
    }

    @Test
    @WithMockUser
    fun `test list topics with non-Kafka data source returns 400`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${nonKafkaDataSource.dataSourceId.value}/topics")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.BAD_REQUEST.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNull()
        assertThat(responseBody.error).isNotNull
        assertThat(responseBody.error!!.code).isEqualTo(ErrorCode.INVALID_DATA)
        assertThat(responseBody.error!!.message).contains("Data source is not a Kafka data source")
        assertThat(responseBody.error!!.message).contains("FOLDER")
    }

    @Test
    @WithMockUser
    fun `test list topics without tenant header returns 500`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
            // No tenant header
        ).andReturn()

        // Should return 500 due to missing tenant ID
        assertThat(result.response.status).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value())
    }
    // ========== EDGE CASES ==========

    @Test
    @WithMockUser
    fun `test list topics with whitespace prefix is treated as no prefix filter`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("topicPrefix", "   ")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!
        val allTestTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix

        // Whitespace prefix should not filter out any of our topics
        assertThat(topics).containsAnyElementsOf(allTestTopics)
    }

    @Test
    @WithMockUser
    fun `test list topics with special characters in prefix`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .param("topicPrefix", "test-")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        assertThat(responseBody.error).isNull()

        val topics = responseBody.data!!

        // Should match topics starting with "test-"
        assertThat(topics).contains("test-topic-with-schema")
        assertThat(topics).contains("test-topic-no-schema")

        // Should not match topics not starting with "test-"
        assertThat(topics).doesNotContain("orders-topic")
        assertThat(topics).doesNotContain("user-events")
    }

    @Test
    @WithMockUser
    fun `test list topics response is properly sorted`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/kafka/${kafkaDataSource.dataSourceId.value}/topics")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())

        val responseBody = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<List<String>>>() {}
        )

        assertThat(responseBody.data).isNotNull
        val topics = responseBody.data!!

        // Filter to only our test topics and verify they are sorted
        val allTestTopics = testTopicsWithSchema + testTopicsWithoutSchema + testTopicsWithPrefix
        val filteredTopics = topics.filter { it in allTestTopics }

        assertThat(filteredTopics).isEqualTo(filteredTopics.sorted())
    }
}
