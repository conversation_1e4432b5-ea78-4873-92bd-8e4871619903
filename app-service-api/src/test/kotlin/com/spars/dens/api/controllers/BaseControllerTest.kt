package com.spars.dens.api.controllers

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import com.spars.dens.core.datamodel.tenant.repository.TenantRepository
import com.spars.dens.core.utils.testing.DensIntegrationTest
import org.jooq.DSLContext
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.test.web.servlet.MockMvc

@AutoConfigureMockMvc
@DensIntegrationTest
class BaseControllerTest {
    @Autowired
    lateinit var mockMvc: MockMvc

    @Autowired
    lateinit var tenantRepository: TenantRepository

    @Autowired
    lateinit var dslContext: DSLContext

    val objectMapper = ObjectMapper()
        .registerKotlinModule()
        .registerModule(JavaTimeModule())

    @BeforeEach
    fun setup() {
        tenantRepository.create(dslContext, TenantFixture().createData)
    }
}
