package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.ErrorCode
import com.spars.dens.api.model.datasource.DataSourceDto
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSourceId
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.models.KafkaSecurityProtocol
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.folder.models.FileSystemProtocol
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.jooq.impl.DSL
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import java.nio.file.Files
import java.nio.file.Path
import java.sql.DriverManager

class DataSourceControllerTest : BaseControllerTest() {

    private val testTenantId = TenantFixture().tenantId

    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    val testDatabaseName = "unit_test_datasource_database"
    val userName = "unit_test_worker"
    val password = "secure_password"

    @ParameterizedTest(name = "test create FOLDER data source for {0} protocol")
    @EnumSource(value = FileSystemProtocol::class)
    @WithMockUser
    fun `test create FOLDER data source`(
        protocol: FileSystemProtocol
    ) {
        val testDataFolderPath = when (protocol) {
            FileSystemProtocol.SFTP -> Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER")
            FileSystemProtocol.FTP -> Path.of("${System.getProperty("user.dir")}/../ftp_data/TEST_FOLDER")
        }
        try {
            Files.createDirectories(testDataFolderPath)
            val createDataSourceRequestBody = """
            {
                "dataSourceName": "MY TEST DATA SOURCE",
                "dataSourceType": "FOLDER",
                "connectionSettings": {
                    "protocol": "$protocol",
                    "host": "localhost",
                    "port": ${getTestPortForProtocol(protocol)},
                    "userName": "user",
                    "password": "password",
                    "_type": "FolderConnectionSettingsDto"
                }
            }
            """.trimIndent()
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createDataSourceRequestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
            ).andReturn()
            assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<DataSourceDto>>(result.response.contentAsString)
            val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
            assertThat(actualResponseBody.data?.dataSourceType).isEqualTo(DataSourceType.FOLDER)
            assertThat(actualResponseBody.data?.dataSourceName).isEqualTo("MY TEST DATA SOURCE")
            val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
            assertThat(dataSources.single().dataSourceId.value).isEqualTo(actualResponseBody.data?.dataSourceId)
        } finally {
            Files.delete(testDataFolderPath)
        }
    }

    @Test
    @WithMockUser
    fun `test create POSTGRESQL data source`() {
        try {
            setupPostgreSQLDatabaseForDataSource()
            val createDataSourceRequestBody = """
                {
                    "dataSourceName": "MY TEST DATA SOURCE",
                    "dataSourceType": "POSTGRESQL",
                    "connectionSettings": {
                        "_type": "PostgreSQLDatabaseConnectionSettingsDto",
                        "userName": "$userName",
                        "password": "$password",
                        "host": "localhost",
                        "port": "5432",
                        "database": "$testDatabaseName"
                    }
                }
            """.trimIndent()
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createDataSourceRequestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
            ).andReturn()
            assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<DataSourceDto>>(result.response.contentAsString)
            val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
            assertThat(actualResponseBody.data?.dataSourceType).isEqualTo(DataSourceType.POSTGRESQL)
            assertThat(actualResponseBody.data?.dataSourceName).isEqualTo("MY TEST DATA SOURCE")
            val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
            assertThat(dataSources.single().dataSourceId.value).isEqualTo(actualResponseBody.data?.dataSourceId)
        } finally {
            cleanupPostgreSQLDatabaseForDataSource()
        }
    }

    @Test
    @WithMockUser
    fun `test create ORACLE data source`() {
        // Oracle database is created by docker-compose file
        val createDataSourceRequestBody = """
            {
                "dataSourceName": "MY TEST DATA SOURCE",
                "dataSourceType": "ORACLE",
                "description": "This is my test data source",
                "connectionSettings": {
                    "_type": "OracleDatabaseConnectionSettingsDto",
                    "userName": "SYSTEM",
                    "password": "$password",
                    "host": "localhost",
                    "port": "1521",
                    "sid": "FREE"
                }
            }
        """.trimIndent()
        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/data-sources")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createDataSourceRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()
        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<DataSourceDto>>(result.response.contentAsString)
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
        assertThat(actualResponseBody.data?.dataSourceType).isEqualTo(DataSourceType.ORACLE)
        assertThat(actualResponseBody.data?.dataSourceName).isEqualTo("MY TEST DATA SOURCE")
        val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
        assertThat(dataSources.single().dataSourceId.value).isEqualTo(actualResponseBody.data?.dataSourceId)
        assertThat(dataSources.single().dataSourceName).isEqualTo(actualResponseBody.data?.dataSourceName)
    }

    @Test
    @WithMockUser
    fun `create POSTGRESQL data source fails when target database does not exists`() {
        try {
            setupPostgreSQLDatabaseForDataSource()
            val createDataSourceRequestBody = """
                {
                    "dataSourceName": "MY TEST DATA SOURCE",
                    "dataSourceType": "POSTGRESQL",
                    "connectionSettings": {
                        "_type": "PostgreSQLDatabaseConnectionSettingsDto",
                        "userName": "$userName",
                        "password": "$password",
                        "host": "localhost",
                        "port": "5432",
                        "database": "NONEXISTINGDATABASE"
                    }
                }
            """.trimIndent()
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createDataSourceRequestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
            ).andReturn()
            assertThat(result.response.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<DataSourceDto>>(result.response.contentAsString)
            val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
            assertThat(actualResponseBody.error?.code).isEqualTo(ErrorCode.INVALID_DATA)
            val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
            assertThat(dataSources).isEmpty()
        } finally {
            cleanupPostgreSQLDatabaseForDataSource()
        }
    }

    @Test
    @WithMockUser
    fun `create data source fails when an existing data source has the same name (case insensitive comparison)`() {
        Files.createDirectories(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER"))
        try {
            val dataSourceFixture = DataSourceFixture(tenantId = testTenantId, dataSourceName = "My Test DATA source")
            dataSourceRepository.create(
                tenantId = testTenantId,
                dslContext = dslContext,
                createData = dataSourceFixture.createData,
            )
            val createDataSourceRequestBody = """
            {
                "dataSourceName": "MY TEST DATA SOURCE",
                "dataSourceType": "FOLDER",
                "connectionSettings": {
                    "protocol": "SFTP",
                    "host": "localhost",
                    "port": 2222,
                    "userName": "user",
                    "password": "password",
                    "_type": "FolderConnectionSettingsDto"
                }
            }
            """.trimIndent()
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createDataSourceRequestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
            ).andReturn()
            assertThat(result.response.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<DataSourceDto>>(result.response.contentAsString)
            val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
            assertThat(actualResponseBody.error?.code).isEqualTo(ErrorCode.UNIQUENESS_VIOLATION)
            val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
            assertThat(dataSources).containsOnly(dataSourceFixture.dataSource)
        } finally {
            Files.delete(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER"))
        }
    }

    @Test
    @WithMockUser
    fun `test list data sources`() {

        val dataSourceFixtureFolder = DataSourceFixture(
            tenantId = testTenantId,
            dataSourceId = DataSourceId.from(5880278223411357008),
            dataSourceName = "My Folder DATA source",
            dataSourceType = DataSourceType.FOLDER,
            connectionSettings = ConnectionSettings.FolderConnectionSettings(
                protocol = FileSystemProtocol.SFTP,
                host = "postgresql.host.local",
                port = 5432,
                userName = "user",
                password = "password"
            )
        )
        val dataSourceFixturePostgreSQL = DataSourceFixture(
            tenantId = testTenantId,
            dataSourceId = DataSourceId.from(8771260675462876958),
            dataSourceName = "My PostgreSQL DATA source",
            dataSourceType = DataSourceType.POSTGRESQL,
            connectionSettings = ConnectionSettings.PostgreSQLDatabaseConnectionSettings(
                host = "postgresql.host.local",
                port = 5432,
                database = "user_events",
                userName = "user",
                password = "password"
            )
        )
        val dataSourceFixtureOracle = DataSourceFixture(
            tenantId = testTenantId,
            dataSourceId = DataSourceId.from(5364020042603172847),
            dataSourceName = "My Oracle DATA source",
            dataSourceType = DataSourceType.ORACLE,
            connectionSettings = ConnectionSettings.OracleDatabaseConnectionSettings(
                host = "postgresql.host.local",
                port = 5432,
                sid = null,
                serviceName = "user_events",
                userName = "user",
                password = "password"
            )
        )
        dataSourceRepository.create(
            tenantId = testTenantId,
            dslContext = dslContext,
            createData = dataSourceFixtureFolder.createData,
        )
        dataSourceRepository.create(
            tenantId = testTenantId,
            dslContext = dslContext,
            createData = dataSourceFixturePostgreSQL.createData,
        )
        dataSourceRepository.create(
            tenantId = testTenantId,
            dslContext = dslContext,
            createData = dataSourceFixtureOracle.createData,
        )
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()
        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<List<DataSourceDto>>>(result.response.contentAsString)
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<List<DataSourceDto>>>() {})
        assertThat(actualResponseBody.data).containsExactlyInAnyOrder(
            DataSourceDto.from(
                dataSourceFixtureFolder.dataSource.copy(
                    connectionSettings = (dataSourceFixtureFolder.dataSource.connectionSettings as ConnectionSettings.FolderConnectionSettings).copy(password = "********")
                )
            ),
            DataSourceDto.from(
                dataSourceFixturePostgreSQL.dataSource.copy(
                    connectionSettings = (dataSourceFixturePostgreSQL.dataSource.connectionSettings as ConnectionSettings.PostgreSQLDatabaseConnectionSettings).copy(password = "********")
                )
            ),
            DataSourceDto.from(
                dataSourceFixtureOracle.dataSource.copy(
                    connectionSettings = (dataSourceFixtureOracle.dataSource.connectionSettings as ConnectionSettings.OracleDatabaseConnectionSettings).copy(password = "********")
                )
            )
        )
        val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
        assertThat(dataSources).containsExactlyInAnyOrder(
            dataSourceFixtureFolder.dataSource,
            dataSourceFixturePostgreSQL.dataSource,
            dataSourceFixtureOracle.dataSource
        )
    }

    @Test
    @WithMockUser
    fun `getDataSource returns data source when it exists`() {
        val dataSourceFixture = DataSourceFixture(
            tenantId = testTenantId,
            dataSourceId = DataSourceId.from(1234567890),
            dataSourceName = "Existing Data Source",
            dataSourceType = DataSourceType.FOLDER,
            connectionSettings = ConnectionSettings.FolderConnectionSettings(
                protocol = FileSystemProtocol.SFTP,
                host = "localhost",
                port = 2222,
                userName = "user",
                password = "password"
            )
        )
        dataSourceRepository.create(
            tenantId = testTenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData,
        )
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/${dataSourceFixture.dataSourceId.value}")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()
        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
        assertThat(actualResponseBody.data).isEqualTo(
            DataSourceDto.from(
                dataSourceFixture.dataSource.copy(
                    connectionSettings = (dataSourceFixture.dataSource.connectionSettings as ConnectionSettings.FolderConnectionSettings).copy(password = "********")
                )
            )
        )
    }

    @Test
    @WithMockUser
    fun `getDataSource returns 404 when data source does not exist`() {
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/data-sources/9999999999")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
        ).andReturn()
        assertThat(result.response.status).isEqualTo(HttpStatus.NOT_FOUND.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
        assertThat(actualResponseBody.error?.code).isEqualTo(ErrorCode.NOT_FOUND)
    }

    @Test
    @WithMockUser
    fun `listSampleFiles returns correct files - REGEX filter`() {
        try {
            // Setup test data
            val dataSourceFixture = DataSourceFixture(
                tenantId = testTenantId,
                dataSourceId = DataSourceId.from(1111111111111111111),
                dataSourceName = "My Folder DATA source - REGEX",
                dataSourceType = DataSourceType.FOLDER,
                connectionSettings = ConnectionSettings.FolderConnectionSettings(
                    protocol = FileSystemProtocol.SFTP,
                    host = "localhost",
                    port = 2222,
                    userName = "user",
                    password = "password"
                )
            )
            dataSourceRepository.create(
                tenantId = testTenantId,
                dslContext = dslContext,
                createData = dataSourceFixture.createData,
            )
            Files.createDirectories(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_REGEX"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_REGEX/file1.txt"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_REGEX/file2.txt"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_REGEX/abc.txt"))

            val requestBody = """
            {
                "folderRelativePath": "data/TEST_FOLDER_REGEX",
                "fileNameFilter": {

                    "_type": "RegexFileNameFilterDto",
                    "pattern": "file.*txt"
                }
            }
            """.trimIndent()

            // Perform the request
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources/folders/${dataSourceFixture.dataSourceId.value}/sample-files")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
            ).andReturn()

            assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
            val actualResponseBody = Json.decodeFromString<ApiResponse<List<String>>>(result.response.contentAsString)
            assertThat(actualResponseBody.data).containsExactlyInAnyOrder("file1.txt", "file2.txt")
        } finally {
            cleanFolder("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_REGEX")
        }
    }

    @ParameterizedTest(name = "listSampleFiles returns correct files for {0} protocol - NONE filter")
    @EnumSource(value = FileSystemProtocol::class)
    @WithMockUser
    fun `listSampleFiles returns correct files - NONE filter`(protocol: FileSystemProtocol) {
        val baseDir = when (protocol) {
            FileSystemProtocol.SFTP -> "${System.getProperty("user.dir")}/../sftp_data"
            FileSystemProtocol.FTP -> "${System.getProperty("user.dir")}/../ftp_data"
        }
        try {
            // Setup test data
            val dataSourceFixture = DataSourceFixture(
                tenantId = testTenantId,
                dataSourceId = DataSourceId.from(2222222222222222222),
                dataSourceName = "My Folder DATA source - NONE",
                dataSourceType = DataSourceType.FOLDER,
                connectionSettings = ConnectionSettings.FolderConnectionSettings(
                    protocol = protocol,
                    host = "localhost",
                    port = getTestPortForProtocol(protocol),
                    userName = "user",
                    password = "password"
                )
            )
            dataSourceRepository.create(
                tenantId = testTenantId,
                dslContext = dslContext,
                createData = dataSourceFixture.createData,
            )
            Files.createDirectories(Path.of("$baseDir/TEST_FOLDER_NONE"))
            Files.createDirectories(Path.of("$baseDir/TEST_FOLDER_NONE/SUB_FOLDER"))
            Files.createFile(Path.of("$baseDir/TEST_FOLDER_NONE/file1.txt"))
            Files.createFile(Path.of("$baseDir/TEST_FOLDER_NONE/file2.txt"))
            Files.createFile(Path.of("$baseDir/TEST_FOLDER_NONE/SUB_FOLDER/abc.txt"))

            val requestBody = """
                {
                    "folderRelativePath": "${if (protocol == FileSystemProtocol.SFTP) "data/" else ""}TEST_FOLDER_NONE"
                }
            """.trimIndent()

            // Perform the request
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources/folders/${dataSourceFixture.dataSourceId.value}/sample-files")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
            ).andReturn()

            assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
            val actualResponseBody = Json.decodeFromString<ApiResponse<List<String>>>(result.response.contentAsString)
            assertThat(actualResponseBody.data).containsExactlyInAnyOrder("file1.txt", "file2.txt", "abc.txt")
        } finally {
            cleanFolder("$baseDir/TEST_FOLDER_NONE")
        }
    }

    @Test
    @WithMockUser
    fun `listSampleFiles returns correct files - OPTIONS filter`() {
        try {
            // Setup test data
            val dataSourceFixture = DataSourceFixture(
                tenantId = testTenantId,
                dataSourceId = DataSourceId.from(3333333333333333333),
                dataSourceName = "My Folder DATA source - OPTIONS",
                dataSourceType = DataSourceType.FOLDER,
                connectionSettings = ConnectionSettings.FolderConnectionSettings(
                    protocol = FileSystemProtocol.SFTP,
                    host = "localhost",
                    port = 2222,
                    userName = "user",
                    password = "password"
                )
            )
            dataSourceRepository.create(
                tenantId = testTenantId,
                dslContext = dslContext,
                createData = dataSourceFixture.createData,
            )
            Files.createDirectories(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS"))
            Files.createDirectories(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS/SUB_FOLDER"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS/apple.txt"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS/banana.csv"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS/apple_banana.csv"))
            Files.createFile(Path.of("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS/SUB_FOLDER/my_apple.csv"))

            val requestBody = """
                {
                    "folderRelativePath": "data/TEST_FOLDER_OPTIONS",
                    "fileNameFilter": {
                        "_type": "OptionsFileNameFilterDto",
                        "contains": "APPLE",
                        "doesNotContain": "baNaNa",
                        "extension": "csv"
                    }
                }
            """.trimIndent()

            // Perform the request
            val result = mockMvc.perform(
                MockMvcRequestBuilders.post("/v1/data-sources/folders/${dataSourceFixture.dataSourceId.value}/sample-files")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(requestBody)
                    .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, testTenantId.value)
            ).andReturn()

            assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
//            val actualResponseBody = Json.decodeFromString<ApiResponse<List<String>>>(result.response.contentAsString)
            val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<List<String>>>() {})
            assertThat(actualResponseBody.data).containsOnly("my_apple.csv")
        } finally {
            cleanFolder("${System.getProperty("user.dir")}/../sftp_data/TEST_FOLDER_OPTIONS")
        }
    }

    @Test
    @WithMockUser
    fun `test create KAFKA data source`() {
        val createDataSourceRequestBody = """
            {
                "dataSourceName": "MY TEST KAFKA DATA SOURCE",
                "dataSourceType": "KAFKA",
                "description": "This is my test Kafka data source",
                "connectionSettings": {
                    "_type": "KafkaConnectionSettingsDto",
                    "bootstrapServers": "localhost:9092",
                    "securityProtocol": "PLAINTEXT",
                    "applicationId": "datapakt",
                    "skipCertificateValidation": true,
                    "saslMechanism": null,
                    "username": null,
                    "password": null
                }
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/data-sources")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createDataSourceRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
        assertThat(actualResponseBody.data?.dataSourceType).isEqualTo(DataSourceType.KAFKA)
        assertThat(actualResponseBody.data?.dataSourceName).isEqualTo("MY TEST KAFKA DATA SOURCE")

        val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
        assertThat(dataSources.single().dataSourceId.value).isEqualTo(actualResponseBody.data?.dataSourceId)
        assertThat(dataSources.single().dataSourceName).isEqualTo(actualResponseBody.data?.dataSourceName)
    }

    @Test
    @WithMockUser
    fun `test create KAFKA data source with SASL authentication`() {
        val createDataSourceRequestBody = """
            {
                "dataSourceName": "MY SECURE KAFKA DATA SOURCE",
                "dataSourceType": "KAFKA",
                "description": "This is my secure Kafka data source",
                "connectionSettings": {
                    "_type": "KafkaConnectionSettingsDto",
                    "bootstrapServers": "localhost:9094",
                    "securityProtocol": "SASL_PLAINTEXT",
                    "applicationId": "datapakt",
                    "skipCertificateValidation": true,
                    "saslMechanism": "PLAIN",
                    "userName": "user",
                    "password": "password"
                }
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/data-sources")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createDataSourceRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<DataSourceDto>>() {})
        assertThat(actualResponseBody.data?.dataSourceType).isEqualTo(DataSourceType.KAFKA)

        val dataSources = dataSourceRepository.listAllDataSources(tenantId = testTenantId, dslContext = dslContext)
        val dataSource = dataSources.single { it.dataSourceId.value == actualResponseBody.data?.dataSourceId }
        assertThat(dataSource.dataSourceName).isEqualTo("MY SECURE KAFKA DATA SOURCE")

        val connectionSettings = dataSource.connectionSettings as ConnectionSettings.KafkaConnectionSettings
        assertThat(connectionSettings.bootstrapServers).isEqualTo("localhost:9094")
        assertThat(connectionSettings.securityProtocol).isEqualTo(KafkaSecurityProtocol.SASL_PLAINTEXT)
    }

    private fun cleanFolder(testDataFolder: String) {
        val mainFolder = Path.of(testDataFolder)
        Files.walk(mainFolder)
            .filter { it != mainFolder }
            // Sort in reverse order so children get deleted before parents
            .sorted(Comparator.reverseOrder())
            // For each file/directory, call Files.delete
            .forEach { Files.delete(it) }
    }

    private fun setupPostgreSQLDatabaseForDataSource() {
        // Can't use dslContext (existing spring bean) due to @Transactional behavior of unit tests.
        // Create table / Drop table statements cannot be called in a transaction
        val connectionAppDatabase = DriverManager.getConnection(
            "*****************************************", "postgres", "password"
        )
        connectionAppDatabase.use { conn ->
            val dslAppDatabase = DSL.using(conn)
            // Step 1: Create the new database
            dslAppDatabase.execute("CREATE DATABASE $testDatabaseName")
            // Step 2: Create the new user
            dslAppDatabase.execute("CREATE USER $userName WITH PASSWORD '$password'")
            // Step 3: Grant the user access to the database
            dslAppDatabase.execute("GRANT CONNECT ON DATABASE $testDatabaseName TO $userName")
        }
        // Step 4: Connect to the new database and grant read access
        val connectionNewDatabase = DriverManager.getConnection(
            "**************************************************", "postgres", "password"
        )
        connectionNewDatabase.use { conn ->
            val dslDataSourceDatabase = DSL.using(conn)
            val schemaName = "public"
            // Grant schema access and SELECT permissions on all tables
            dslDataSourceDatabase.execute("GRANT USAGE ON SCHEMA $schemaName TO $userName")
            dslDataSourceDatabase.execute("GRANT SELECT ON ALL TABLES IN SCHEMA $schemaName TO $userName")

            // Ensure default privileges for future tables
            dslDataSourceDatabase.execute("ALTER DEFAULT PRIVILEGES IN SCHEMA $schemaName GRANT SELECT ON TABLES TO $userName")
        }
    }

    private fun cleanupPostgreSQLDatabaseForDataSource() {
        // Can't use dslContext (existing spring bean) due to @Transactional behavior of unit tests.
        // Create table / Drop table statements cannot be called in a transaction
        val connectionAppDatabase = DriverManager.getConnection(
            "*****************************************", "postgres", "password"
        )
        connectionAppDatabase.use { conn ->
            val dslAppDatabase = DSL.using(conn)
            dslAppDatabase.execute(
                "SELECT pg_terminate_backend(pg_stat_activity.pid) " +
                    "FROM pg_stat_activity " +
                    "WHERE pg_stat_activity.datname = '$testDatabaseName' " +
                    "  AND pid <> pg_backend_pid()"
            )
            dslAppDatabase.execute("DROP DATABASE IF EXISTS $testDatabaseName")
            dslAppDatabase.execute("DROP USER IF EXISTS $userName")
        }
    }

    private fun getTestPortForProtocol(protocol: FileSystemProtocol): Int {
        return when (protocol) {
            FileSystemProtocol.FTP -> 21
            FileSystemProtocol.SFTP -> 2222
        }
    }
}
