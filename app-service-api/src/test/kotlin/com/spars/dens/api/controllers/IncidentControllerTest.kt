package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.ErrorCode
import com.spars.dens.api.model.GenericItemListResponseDto
import com.spars.dens.api.model.IncidentDto
import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.incident.models.Incident
import com.spars.dens.core.datamodel.incident.models.IncidentStatus
import com.spars.dens.core.datamodel.incident.repositories.IncidentRepository
import com.spars.dens.core.datamodel.rule.fixtures.AssetRuleFixture
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours

class IncidentControllerTest : BaseControllerTest() {

    @Autowired
    lateinit var incidentRepository: IncidentRepository

    @Autowired
    lateinit var ruleRepository: RuleRepository

    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    lateinit var assetRepository: AssetRepository

    private val tenantFixture = TenantFixture()
    private val dataSourceFixture = DataSourceFixture()
    private val assetFixture = AssetFixture()
    private val assetRuleFixture = AssetRuleFixture(ruleId = RuleId.from(1234567890L))
    private val fieldRuleFixture = FieldRuleFixture(ruleId = RuleId.from(2345678901L))

    @BeforeEach
    override fun setup() {
        super.setup()
        // Setup test data
        dataSourceRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )
        assetRepository.create(tenantFixture.tenantId, dslContext, assetFixture.createData)
        ruleRepository.create(tenantFixture.tenantId, dslContext, listOf(assetRuleFixture.createData, fieldRuleFixture.createData))
    }

    @Test
    @WithMockUser
    fun `test list incidents with default parameters`() {
        // Setup - create some incidents
        val now = Clock.System.now()

        // Create incidents within the default time range (last 30 days)
        createTestIncidents(5, now.minus(15.days))

        // Execute request with default parameters
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data!!.items).hasSize(5) // Default limit is 10, we created 5
        assertThat(response.data!!.totalCount).isEqualTo(5)
        assertThat(response.data!!.page).isEqualTo(1)
        assertThat(response.data!!.limit).isEqualTo(10)
        assertThat(response.data!!.items.first().createdAt > response.data!!.items.last().createdAt).isTrue() // Default sort is desc
    }

    @Test
    @WithMockUser
    fun `test list incidents with custom date range`() {
        // Setup - create incidents at different times
        val now = Clock.System.now()

        // Create incidents at different time periods
        createTestIncidents(3, now.minus(5.days)) // Within custom range
        createTestIncidents(2, now.minus(15.days)) // Outside custom range

        // Custom date range: 7 days ago to now
        val startDate = now.minus(7.days).toEpochMilliseconds()
        val endDate = now.toEpochMilliseconds()

        // Execute request with custom date range
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("startDate", startDate.toString())
                .param("endDate", endDate.toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data!!.items).hasSize(3) // Only incidents within the custom range
        assertThat(response.data!!.totalCount).isEqualTo(3)
        assertThat(response.data!!.page).isEqualTo(1)
        assertThat(response.data!!.limit).isEqualTo(10)
    }

    @Test
    @WithMockUser
    fun `test list incidents with pagination`() {
        // Setup - create more incidents than the default limit
        val now = Clock.System.now()
        createTestIncidents(15, now.minus(5.days))

        // First page (default limit = 10)
        val resultPage1 = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("page", "1")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Second page
        val resultPage2 = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("page", "2")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify first page
        val responsePage1 = objectMapper.readValue(
            resultPage1.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(responsePage1.data).isNotNull
        assertThat(responsePage1.data!!.items).hasSize(10)
        assertThat(responsePage1.data!!.totalCount).isEqualTo(15)
        assertThat(responsePage1.data!!.page).isEqualTo(1)
        assertThat(responsePage1.data!!.limit).isEqualTo(10)

        // Verify second page
        val responsePage2 = objectMapper.readValue(
            resultPage2.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(responsePage2.data).isNotNull
        assertThat(responsePage2.data!!.items).hasSize(5)
        assertThat(responsePage2.data!!.totalCount).isEqualTo(15)
        assertThat(responsePage2.data!!.page).isEqualTo(2)
        assertThat(responsePage2.data!!.limit).isEqualTo(10)

        // Verify different incidents on each page
        val page1Ids = responsePage1.data?.items?.map { it.incidentId }?.toSet() ?: emptySet()
        val page2Ids = responsePage2.data?.items?.map { it.incidentId }?.toSet() ?: emptySet()
        assertThat(page1Ids.intersect(page2Ids)).isEmpty()
    }

    @Test
    @WithMockUser
    fun `test list incidents with custom limit`() {
        // Setup - create incidents
        val now = Clock.System.now()
        createTestIncidents(10, now.minus(5.days))

        // Execute request with custom limit
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("limit", "5")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(response.data).isNotNull
        assertThat(response.data!!.items).hasSize(5) // Custom limit
        assertThat(response.data!!.totalCount).isEqualTo(10)
        assertThat(response.data!!.page).isEqualTo(1)
        assertThat(response.data!!.limit).isEqualTo(5)
    }

    @Test
    @WithMockUser
    fun `test list incidents filtered by status`() {
        // Setup - create incidents with different statuses
        val now = Clock.System.now()

        // Create active incidents
        createTestIncidents(3, now.minus(5.days), IncidentStatus.ACTIVE)

        // Create acknowledged incidents
        createTestIncidents(2, now.minus(4.days), IncidentStatus.ACKNOWLEDGED)

        // Create resolved incidents
        createTestIncidents(4, now.minus(3.days), IncidentStatus.RESOLVED)

        // Execute request filtering by ACTIVE status
        val resultActive = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("statuses", "ACTIVE")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Execute request filtering by multiple statuses
        val resultMultiple = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("statuses", "ACTIVE", "ACKNOWLEDGED")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify active only
        val responseActive = objectMapper.readValue(
            resultActive.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(responseActive.data).isNotNull
        assertThat(responseActive.data!!.items).hasSize(3)
        assertThat(responseActive.data!!.totalCount).isEqualTo(3)
        assertThat(responseActive.data!!.page).isEqualTo(1)
        assertThat(responseActive.data!!.limit).isEqualTo(10)
        assertThat(responseActive.data!!.items.all { it.status == IncidentStatus.ACTIVE }).isTrue()

        // Verify multiple statuses
        val responseMultiple = objectMapper.readValue(
            resultMultiple.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(responseMultiple.data).isNotNull
        assertThat(responseMultiple.data!!.items).hasSize(5)
        assertThat(responseMultiple.data!!.totalCount).isEqualTo(5)
        assertThat(responseMultiple.data!!.page).isEqualTo(1)
        assertThat(responseMultiple.data!!.limit).isEqualTo(10)
        assertThat(
            responseMultiple.data!!.items.all { it.status == IncidentStatus.ACTIVE || it.status == IncidentStatus.ACKNOWLEDGED }
        ).isTrue()
    }

    @Test
    @WithMockUser
    fun `test list incidents with custom sorting`() {
        // Setup - create incidents
        val now = Clock.System.now()

        // Create incidents with different statuses
        createTestIncidents(2, now.minus(5.days), IncidentStatus.ACTIVE)
        createTestIncidents(2, now.minus(4.days), IncidentStatus.ACKNOWLEDGED)
        createTestIncidents(2, now.minus(3.days), IncidentStatus.RESOLVED)

        // Execute request sorting by status ascending
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("sort", "status")
                .param("sortDirection", "asc")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )

        // Check that incidents are sorted by status in ascending order
        val statuses = response.data?.items?.map { it.status }
        assertThat(statuses).isEqualTo(statuses?.sorted())
    }

    @Test
    @WithMockUser
    fun `test list incidents with invalid sort parameter`() {
        // Execute request with invalid sort parameter
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("sort", "invalidField")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(400)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(response.error?.message).contains("Sort parameter must be either 'createdAt' or 'status'")
    }

    @Test
    @WithMockUser
    fun `test list incidents with invalid pagination parameters`() {
        // Execute request with invalid page number
        val resultInvalidPage = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("page", "0")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Execute request with invalid limit
        val resultInvalidLimit = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .param("limit", "0")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify invalid page
        assertThat(resultInvalidPage.response.status).isEqualTo(400)
        val responseInvalidPage = objectMapper.readValue(
            resultInvalidPage.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(responseInvalidPage.error?.message).contains("Page must be greater than or equal to 1")

        // Verify invalid limit
        assertThat(resultInvalidLimit.response.status).isEqualTo(400)
        val responseInvalidLimit = objectMapper.readValue(
            resultInvalidLimit.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(responseInvalidLimit.error?.message).contains("Limit must be greater than or equal to 1")
    }

    @Test
    @WithMockUser
    fun `test list incidents without tenant id header`() {
        // Execute request without tenant ID header
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .contentType(MediaType.APPLICATION_JSON)
            // No tenant ID header
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(500)
    }

    @Test
    @WithMockUser
    fun `test list incidents with empty result`() {
        // No incidents created

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<GenericItemListResponseDto<IncidentDto>>>() {}
        )
        assertThat(response.data).isNotNull
        assertThat(response.data!!.items).isEmpty()
        assertThat(response.data!!.totalCount).isEqualTo(0)
        assertThat(response.data!!.page).isEqualTo(1)
        assertThat(response.data!!.limit).isEqualTo(10)
    }

    // Helper method to create test incidents
    private fun createTestIncidents(count: Int, createdAt: Instant, status: IncidentStatus = IncidentStatus.ACTIVE) {
        repeat(count) { index ->
            val incident = Incident.CreateData(
                tenantId = tenantFixture.tenantId,
                ruleId = if (index % 2 == 0) assetRuleFixture.ruleId else fieldRuleFixture.ruleId,
                createdAt = createdAt.plus((index * 2).hours),
                status = status
            )
            incidentRepository.create(tenantFixture.tenantId, dslContext, incident)
        }
    }

    @Test
    @WithMockUser
    fun `test update incident status to acknowledged`() {
        // Setup - create an incident
        val now = Clock.System.now()
        val incident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = now.minus(1.days),
            status = IncidentStatus.ACTIVE
        )

        val createdIncidentId = incidentRepository.create(
            tenantFixture.tenantId, dslContext, incident
        ).incidentId

        // Create request body
        val requestBody = """
            {
                "status": "ACKNOWLEDGED",
                "notes": "Investigating this issue"
            }
        """.trimIndent()

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.put("/v1/incidents/${createdIncidentId.value}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify response
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<IncidentDto>>() {}
        )

        // Verify response data
        assertThat(response.data).isNotNull
        assertThat(response.data?.incidentId).isEqualTo(createdIncidentId.value)
        assertThat(response.data?.status).isEqualTo(IncidentStatus.ACKNOWLEDGED)
        assertThat(response.data?.notes).contains("Investigating this issue")
        assertThat(response.data?.acknowledgedAt).isNotNull()

        // Verify database state
        val updatedIncident = incidentRepository.getByIds(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(createdIncidentId)
        ).first()

        assertThat(updatedIncident.status).isEqualTo(IncidentStatus.ACKNOWLEDGED)
        assertThat(updatedIncident.notes).contains("Investigating this issue")
        assertThat(updatedIncident.acknowledgedAt).isNotNull()
    }

    @Test
    @WithMockUser
    fun `test update incident status to resolved`() {
        // Setup - create an acknowledged incident
        val now = Clock.System.now()
        val incident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = fieldRuleFixture.ruleId,
            createdAt = now.minus(2.days),
            status = IncidentStatus.ACKNOWLEDGED,
            acknowledgedAt = now.minus(1.days),
            notes = "Previously acknowledged"
        )

        val createdIncidentId = incidentRepository.create(
            tenantFixture.tenantId, dslContext, incident
        ).incidentId

        // Create request body
        val requestBody = """
            {
                "status": "RESOLVED",
                "notes": "Fixed the data quality issue"
            }
        """.trimIndent()

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.put("/v1/incidents/${createdIncidentId.value}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify response
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<IncidentDto>>() {}
        )

        // Verify response data
        assertThat(response.data).isNotNull
        assertThat(response.data?.incidentId).isEqualTo(createdIncidentId.value)
        assertThat(response.data?.status).isEqualTo(IncidentStatus.RESOLVED)
        assertThat(response.data?.notes).contains("Fixed the data quality issue")
        assertThat(response.data?.resolvedAt).isNotNull()

        // Verify database state
        val updatedIncident = incidentRepository.getByIds(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(createdIncidentId)
        ).first()

        assertThat(updatedIncident.status).isEqualTo(IncidentStatus.RESOLVED)
        assertThat(updatedIncident.notes).contains("Fixed the data quality issue")
        assertThat(updatedIncident.resolvedAt).isNotNull()
        // Verify that acknowledged data is preserved
        assertThat(updatedIncident.notes).contains("Previously acknowledged")
        assertThat(updatedIncident.acknowledgedAt).isNotNull()
    }

    @Test
    @WithMockUser
    fun `test update incident status to active`() {
        // Setup - create a resolved incident
        val now = Clock.System.now()
        val incident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = now.minus(3.days),
            status = IncidentStatus.RESOLVED,
            acknowledgedAt = now.minus(2.days),
            resolvedAt = now.minus(1.days),
            notes = "Previously resolved"
        )

        val createdIncidentId = incidentRepository.create(
            tenantFixture.tenantId, dslContext, incident
        ).incidentId

        // Create request body to set back to active
        val requestBody = """
            {
                "status": "ACTIVE"
            }
        """.trimIndent()

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.put("/v1/incidents/${createdIncidentId.value}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify response
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<IncidentDto>>() {}
        )

        // Verify response data
        assertThat(response.data).isNotNull
        assertThat(response.data?.incidentId).isEqualTo(createdIncidentId.value)
        assertThat(response.data?.status).isEqualTo(IncidentStatus.ACTIVE)

        // Verify database state
        val updatedIncident = incidentRepository.getByIds(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(createdIncidentId)
        ).first()

        assertThat(updatedIncident.status).isEqualTo(IncidentStatus.ACTIVE)
    }

    @Test
    @WithMockUser
    fun `test update incident status - incident not found`() {
        // Use a non-existent incident ID
        val nonExistentIncidentId = 999999L

        // Create request body
        val requestBody = """
            {
                "status": "ACKNOWLEDGED",
                "notes": "This incident doesn't exist"
            }
        """.trimIndent()

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.put("/v1/incidents/$nonExistentIncidentId/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify response
        assertThat(result.response.status).isEqualTo(HttpStatus.NOT_FOUND.value())
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<IncidentDto>>() {}
        )

        // Verify error response
        assertThat(response.error).isNotNull
        assertThat(response.error?.code).isEqualTo(ErrorCode.NOT_FOUND)
        assertThat(response.error?.message).contains("Incident not found")
    }

    @Test
    @WithMockUser
    fun `test update incident status without tenant id header`() {
        // Setup - create an incident
        val now = Clock.System.now()
        val incident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = now.minus(1.days)
        )

        val createdIncidentId = incidentRepository.create(
            tenantFixture.tenantId, dslContext, incident
        ).incidentId

        // Create request body
        val requestBody = """
            {
                "status": "ACKNOWLEDGED",
                "notes": "Missing tenant header"
            }
        """.trimIndent()

        // Execute request without tenant ID header
        val result = mockMvc.perform(
            MockMvcRequestBuilders.put("/v1/incidents/${createdIncidentId.value}/status")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
            // No tenant ID header
        ).andReturn()

        // Verify response
        assertThat(result.response.status).isEqualTo(500)
    }
}
