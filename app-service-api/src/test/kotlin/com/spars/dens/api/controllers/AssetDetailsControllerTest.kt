package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.ErrorCode
import com.spars.dens.api.model.asset.AssetDetailsResponseDto
import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.incident.models.Incident
import com.spars.dens.core.datamodel.incident.repositories.IncidentRepository
import com.spars.dens.core.datamodel.rule.fixtures.AssetRuleFixture
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.models.RuleSetExecution
import com.spars.dens.core.datamodel.rule.models.RuleSetExecutionId
import com.spars.dens.core.datamodel.rule.models.RuleSetExecutionResultDetails
import com.spars.dens.core.datamodel.rule.models.SingleRuleExecutionResult
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.rule.repositories.RuleSetExecutionRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes

class AssetDetailsControllerTest : BaseControllerTest() {
    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    lateinit var assetRepository: AssetRepository

    @Autowired
    lateinit var ruleRepository: RuleRepository

    @Autowired
    lateinit var ruleSetExecutionRepository: RuleSetExecutionRepository

    @Autowired
    lateinit var incidentRepository: IncidentRepository

    private val tenantFixture = TenantFixture()
    private val dataSourceFixture = DataSourceFixture()
    private val assetFixture = AssetFixture()
    private val assetRuleFixture = AssetRuleFixture(ruleId = RuleId.from(1234567890L))
    private val fieldRuleFixture = FieldRuleFixture(ruleId = RuleId.from(2345678901L))

    @BeforeEach
    override fun setup() {
        super.setup()
        // Create data source
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        // Create asset
        assetRepository.create(assetFixture.tenantId, dslContext, assetFixture.createData)

        // Create rules
        ruleRepository.create(
            assetRuleFixture.tenantId,
            dslContext,
            listOf(assetRuleFixture.createData, fieldRuleFixture.createData)
        )
    }

    @Test
    @WithMockUser
    fun `test get asset details with metrics`() {
        // Create rule set executions
        val now = Clock.System.now()

        // Create rule set execution for the asset
        createRuleSetExecution(
            assetId = assetFixture.assetId,
            ruleIds = listOf(assetRuleFixture.ruleId, fieldRuleFixture.ruleId),
            createdAt = now.minus(3.days),
            numRowsScanned = 1000L,
            numRowsPassingAllRules = 950L,
            numRowsFailingAnyRule = 50L,
            overallDataQualityScore = 0.95
        )

        // Create incidents
        createIncident(assetRuleFixture.ruleId, now.minus(2.days))
        createIncident(fieldRuleFixture.ruleId, now.minus(1.days))

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/assets/${assetFixture.assetId.value}/details")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<AssetDetailsResponseDto>>() {}
        )

        // Verify response
        assertThat(response.data).isNotNull
        assertThat(response.data!!.asset.assetId).isEqualTo(assetFixture.assetId.value)
        assertThat(response.data!!.metrics.totalRecordsScanned.value).isEqualTo(1000.0)
        assertThat(response.data!!.metrics.dataQualityScore.value).isEqualTo(0.95)
        assertThat(response.data!!.metrics.incidents).hasSize(2)
    }

    @Test
    @WithMockUser
    fun `test get asset details with custom date range`() {
        // Create rule set executions
        val now = Clock.System.now()
        val customStartTime = now.minus(30.days)
        val customEndTime = now.minus(20.days)

        // Create rule set execution within the custom date range
        createRuleSetExecution(
            assetId = assetFixture.assetId,
            ruleIds = listOf(assetRuleFixture.ruleId),
            createdAt = now.minus(25.days),
            numRowsScanned = 500L,
            numRowsPassingAllRules = 450L,
            numRowsFailingAnyRule = 50L,
            overallDataQualityScore = 0.9
        )

        // Create rule set execution outside the custom date range
        createRuleSetExecution(
            assetId = assetFixture.assetId,
            ruleIds = listOf(fieldRuleFixture.ruleId),
            createdAt = now.minus(15.days),
            numRowsScanned = 1000L,
            numRowsPassingAllRules = 950L,
            numRowsFailingAnyRule = 50L,
            overallDataQualityScore = 0.95
        )

        // Execute request with custom date range
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/assets/${assetFixture.assetId.value}/details")
                .param("startDate", customStartTime.toEpochMilliseconds().toString())
                .param("endDate", customEndTime.toEpochMilliseconds().toString())
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<AssetDetailsResponseDto>>() {}
        )

        // Verify response - should only include metrics from the first execution
        assertThat(response.data).isNotNull
        assertThat(response.data!!.metrics.totalRecordsScanned.value).isEqualTo(500.0)
        assertThat(response.data!!.metrics.dataQualityScore.value).isEqualTo(0.9)
    }

    @Test
    @WithMockUser
    fun `test get asset details for non-existent asset`() {
        val nonExistentAssetId = 999999L

        // Execute request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/assets/$nonExistentAssetId/details")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(HttpStatus.NOT_FOUND.value())
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<AssetDetailsResponseDto>>() {}
        )

        // Verify error response
        assertThat(response.error).isNotNull
        assertThat(response.error!!.code).isEqualTo(ErrorCode.NOT_FOUND)
    }

    private fun createRuleSetExecution(
        assetId: com.spars.dens.core.datamodel.asset.models.AssetId,
        ruleIds: List<RuleId>,
        createdAt: Instant,
        numRowsScanned: Long,
        numRowsPassingAllRules: Long,
        numRowsFailingAnyRule: Long,
        overallDataQualityScore: Double
    ) {
        val resultDetails = RuleSetExecutionResultDetails(
            dataChunkId = null,
            singleRuleExecutionResults = ruleIds.map { ruleId ->
                SingleRuleExecutionResult(
                    ruleId = ruleId,
                    numRowsScanned = numRowsScanned / ruleIds.size,
                    numRowsPassed = numRowsPassingAllRules / ruleIds.size,
                    numRowsFailed = numRowsFailingAnyRule / ruleIds.size,
                    sampleFailingValues = emptyList()
                )
            }
        )

        val createData = RuleSetExecution.CreateData(
            tenantId = tenantFixture.tenantId,
            executionId = RuleSetExecutionId.from(System.nanoTime()),
            assetId = assetId,
            ruleIds = ruleIds,
            plannedStartTime = createdAt.minus(1.hours),
            startTime = createdAt.minus(30.minutes),
            endTime = createdAt,
            numRowsScanned = numRowsScanned,
            numRowsPassingAllRules = numRowsPassingAllRules,
            numRowsFailingAnyRule = numRowsFailingAnyRule,
            numRowsSkipped = 0L,
            isCompleteSchemaViolation = false,
            overallDataQualityScore = overallDataQualityScore,
            resultDetails = resultDetails,
            createdAt = createdAt
        )

        ruleSetExecutionRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = createData
        )
    }

    private fun createIncident(
        ruleId: RuleId,
        createdAt: Instant
    ) {
        val incident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = ruleId,
            createdAt = createdAt
        )

        incidentRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = incident
        )
    }
}
