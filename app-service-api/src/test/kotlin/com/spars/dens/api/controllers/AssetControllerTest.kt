package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.ErrorCode
import com.spars.dens.api.model.asset.AssetDto
import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.folder.models.FileSystemProtocol
import com.spars.dens.core.datamodel.rule.fixtures.AssetRuleFixture
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.RuleType
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.util.ResourceUtils

class AssetControllerTest : BaseControllerTest() {
    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    lateinit var assetRepository: AssetRepository

    @Autowired
    lateinit var ruleRepository: RuleRepository

    private fun loadRequestBody(filename: String, dataSourceId: Long): String {
        return ResourceUtils.getFile("classpath:test_requests/assets/$filename")
            .readText()
            .replace("\${dataSourceId}", dataSourceId.toString())
    }

    @Test
    @WithMockUser
    fun `test list assets`() {
        val dataSourceFixture = DataSourceFixture()
        val assetFixture = AssetFixture()
        val assetRuleFixture = AssetRuleFixture()
        val fieldRuleFixture = FieldRuleFixture()
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)
        assetRepository.create(assetFixture.tenantId, dslContext, assetFixture.createData)
        ruleRepository.create(assetRuleFixture.tenantId, dslContext, listOf(assetRuleFixture.createData, fieldRuleFixture.createData))
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/assets")
                .contentType(MediaType.APPLICATION_JSON)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()
        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<List<AssetDto>>>() {})
        assertThat(actualResponseBody.data).containsOnly(AssetDto.from(assetFixture.asset))
    }

    @Test
    @WithMockUser
    fun `test create data folder asset - valid input without rules`() {
        val dataSourceFixture = DataSourceFixture(
            dataSourceType = DataSourceType.FOLDER,
            connectionSettings = ConnectionSettings.FolderConnectionSettings(
                protocol = FileSystemProtocol.SFTP,
                host = "localhost",
                port = 2222,
                userName = "user",
                password = "password"
            )
        )
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        val createAssetRequestBody = loadRequestBody("create_data_folder_asset_no_rules.json", dataSourceFixture.dataSourceId.value)

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/assets/folder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createAssetRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<AssetDto>>() {})

        assertThat(actualResponseBody.data?.assetName).isEqualTo("Test CSV Asset")
        assertThat(actualResponseBody.data?.rules).isEmpty()
    }

    @Test
    @WithMockUser
    fun `test create data folder asset - with asset level rules`() {
        val dataSourceFixture = DataSourceFixture(dataSourceType = DataSourceType.FOLDER)
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        val createAssetRequestBody = loadRequestBody("create_data_folder_asset_with_asset_rules.json", dataSourceFixture.dataSourceId.value)

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/assets/folder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createAssetRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<AssetDto>>() {})

        assertThat(actualResponseBody.data?.rules).hasSize(2)
        assertThat(actualResponseBody.data?.rules?.map { it.ruleType }).containsExactlyInAnyOrder(
            RuleType.ASSET_FRESHNESS,
            RuleType.ASSET_VOLUME
        )
    }

    @Test
    @WithMockUser
    fun `test create data folder asset - with field level rules`() {
        val dataSourceFixture = DataSourceFixture(dataSourceType = DataSourceType.FOLDER)
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        val createAssetRequestBody = ResourceUtils.getFile("classpath:test_requests/assets/create_data_folder_asset_with_field_rules.json")
            .readText()
            .replace("\${dataSourceId}", dataSourceFixture.dataSourceId.value.toString())

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/assets/folder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createAssetRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.OK.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<AssetDto>>() {})

        assertThat(actualResponseBody.data?.rules).hasSize(2)
        assertThat(actualResponseBody.data?.rules?.map { it.ruleType }).containsExactlyInAnyOrder(
            RuleType.FIELD_NOT_NULL,
            RuleType.FIELD_RANGE
        )
    }

    @Test
    @WithMockUser
    fun `test create data folder asset - invalid schema (empty columns)`() {
        val dataSourceFixture = DataSourceFixture(dataSourceType = DataSourceType.FOLDER)
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        val createAssetRequestBody = ResourceUtils.getFile("classpath:test_requests/assets/create_data_folder_asset_empty_schema.json")
            .readText()
            .replace("\${dataSourceId}", dataSourceFixture.dataSourceId.value.toString())

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/assets/folder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createAssetRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<AssetDto>>() {})
        assertThat(actualResponseBody.error?.code).isEqualTo(ErrorCode.INVALID_DATA)
    }

    @Test
    @WithMockUser
    fun `test create data folder asset - invalid field rule (field name not in schema)`() {
        val dataSourceFixture = DataSourceFixture(dataSourceType = DataSourceType.FOLDER)
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        val createAssetRequestBody = loadRequestBody("create_data_folder_asset_invalid_rule_field.json", dataSourceFixture.dataSourceId.value)

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/assets/folder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createAssetRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<AssetDto>>() {})
        assertThat(actualResponseBody.error?.code).isEqualTo(ErrorCode.INVALID_DATA)
    }

    @Test
    @WithMockUser
    fun `test create data folder asset - invalid cron expression`() {
        val dataSourceFixture = DataSourceFixture(dataSourceType = DataSourceType.FOLDER)
        dataSourceRepository.create(dataSourceFixture.tenantId, dslContext, dataSourceFixture.createData)

        val createAssetRequestBody = loadRequestBody("create_data_folder_asset_invalid_cron.json", dataSourceFixture.dataSourceId.value)

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/assets/folder")
                .contentType(MediaType.APPLICATION_JSON)
                .content(createAssetRequestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, TenantFixture().tenantId.value)
        ).andReturn()

        assertThat(result.response.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
        val actualResponseBody = objectMapper.readValue(result.response.contentAsString, object : TypeReference<ApiResponse<AssetDto>>() {})
        assertThat(actualResponseBody.error?.code).isEqualTo(ErrorCode.INVALID_DATA)
    }
}
