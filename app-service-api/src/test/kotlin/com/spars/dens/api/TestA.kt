package com.spars.dens.api

import java.sql.DriverManager
import java.sql.ResultSet
import java.sql.Types
import java.util.*
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

fun main() {
//    val json = Json {
//        encodeDefaults = true
//        classDiscriminator = "_type"
//    }
//    val dataSourceSettings: DataSourceSettings = DataSourceSettings.FolderDataSourceSettings(rootPath = "/abc/def", subPath = null)
//    val str = json.encodeToString(dataSourceSettings)
//    println(str)
//    val deserialized = json.decodeFromString<DataSourceSettings>(str)
//    println(deserialized)

//    val notNullRule: ValidationRule = ValidationRule.FieldNotNullValidationRule()
//    val serializedNotNullRule = ValidationRule.JSON.encodeToString(notNullRule)
//    println(serializedNotNullRule)
//
//    val freshnessRule: ValidationRule = ValidationRule.AssetFreshnessValidationRule(maxLagMinutes = 1440)
//    val serializedFreshnessRule = ValidationRule.JSON.encodeToString(freshnessRule)
//    println(serializedFreshnessRule)
//
//    val rangeRule: ValidationRule = ValidationRule.FieldRangeValidationRule(
//        minValue = ComparableValue.NumberValue(3),
//        minInclusive = true,
//        maxValue = ComparableValue.NumberValue(5),
//        maxInclusive = false
//    )
//    val serializedRangeRule = ValidationRule.JSON.encodeToString(rangeRule)
//    println(serializedRangeRule)
//
//    val deserializedNotNull = ValidationRule.JSON.decodeFromString<ValidationRule>(serializedNotNullRule)
//    val deserializedRange = ValidationRule.JSON.decodeFromString<ValidationRule>(serializedRangeRule)
//    println(deserializedNotNull)
//    println(deserializedRange)
//    ftpsPrintFiles()
    // val client: OpenLineageClient = Clients.newClient()

//    listTables()
//    testJavaThreads()
    testVirtualThreads()
}

fun sqlTypeToJavaClass(sqlType: Int, typeName: String): Class<*>? {
    return when (sqlType) {
        Types.CHAR, Types.VARCHAR, Types.LONGVARCHAR,
        Types.NCHAR, Types.NVARCHAR, Types.LONGNVARCHAR -> String::class.java
        Types.NUMERIC, Types.DECIMAL -> java.math.BigDecimal::class.java
        Types.BIT, Types.BOOLEAN -> java.lang.Boolean::class.java
        Types.TINYINT -> java.lang.Byte::class.java
        Types.SMALLINT -> java.lang.Short::class.java
        Types.INTEGER -> java.lang.Integer::class.java
        Types.BIGINT -> java.lang.Long::class.java
        Types.REAL -> java.lang.Float::class.java
        Types.FLOAT, Types.DOUBLE -> java.lang.Double::class.java
        Types.BINARY, Types.VARBINARY, Types.LONGVARBINARY -> ByteArray::class.java
        Types.DATE -> java.sql.Date::class.java
        Types.TIME -> java.sql.Time::class.java
        Types.TIMESTAMP -> {
            // PostgreSQL: typeName "timestamptz" indicates "timestamp with time zone"
            if (typeName.equals("timestamptz", ignoreCase = true)) {
                java.time.OffsetDateTime::class.java
            } else {
                java.sql.Timestamp::class.java
            }
        }
        Types.CLOB -> java.sql.Clob::class.java
        Types.BLOB -> java.sql.Blob::class.java
        Types.ARRAY -> java.sql.Array::class.java
        Types.STRUCT -> java.sql.Struct::class.java
        Types.REF -> java.sql.Ref::class.java
        else -> null
    }
}

fun listTables() {
//    val connectionUrl = "*****************************************"
//    val ds = DatabaseConnectionSources.newDatabaseConnectionSource(
//        connectionUrl, MultiUseUserCredentials("postgres", "password")
//    )
//    val limitOptionsBuilder =
//        LimitOptionsBuilder.builder()
//
//    val loadOptionsBuilder =
//        LoadOptionsBuilder.builder()
//            // Set what details are required in the schema - this affects the
//            // time taken to crawl the schema
//            .withSchemaInfoLevel(SchemaInfoLevelBuilder.detailed())
//    val options =
//        SchemaCrawlerOptionsBuilder.newSchemaCrawlerOptions()
//            .withLimitOptions(limitOptionsBuilder.toOptions())
//            .withLoadOptions(loadOptionsBuilder.toOptions());
//
//    // Get the schema definition
//
//    val catalog = SchemaCrawlerUtility.getCatalog(ds, options);
//
//    for (schema in catalog.schemas) {
//        println(schema.toString())
//        for (table in catalog.getTables(schema)) {
//            print("o--> $table");
//            if (table is View) {
//                println(" (VIEW)");
//            } else {
//                println();
//            }
//
//            for (column in table.columns) {
//                println("     o--> $column (${column.columnDataType})");
//            }
//        }
//    }
    // Replace with your actual connection details.
    val url = "*****************************************"
    val user = "postgres"
    val password = "password"

    DriverManager.getConnection(url, user, password).use { connection ->
        val metaData = connection.metaData

        // Iterate over all schemas.
        metaData.schemas.use { schemas: ResultSet ->
            while (schemas.next()) {
                val schemaName = schemas.getString("TABLE_SCHEM")
                println("Schema: $schemaName")

                // Retrieve tables and views in the current schema.
                val types = arrayOf("TABLE", "VIEW")
                metaData.getTables(null, schemaName, "%", types).use { tables: ResultSet ->
                    while (tables.next()) {
                        val tableName = tables.getString("TABLE_NAME")
                        val tableType = tables.getString("TABLE_TYPE")
                        println("  $tableType: $tableName")

                        // Retrieve all columns for the current table/view.
                        metaData.getColumns(null, schemaName, tableName, "%").use { columns: ResultSet ->
                            while (columns.next()) {
                                val columnName = columns.getString("COLUMN_NAME")
                                val dataType = columns.getInt("DATA_TYPE")
                                val typeName = columns.getString("TYPE_NAME")
                                val javaClass = sqlTypeToJavaClass(dataType, typeName)
                                println(
                                    "    Column: $columnName, SQL Type: $dataType ($typeName), " +
                                        "Java Class: ${javaClass?.name ?: "Unknown"}"
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/*
fun ftpsPrintFiles() {
    val trustAllCertificates = object : X509TrustManager {
        override fun checkClientTrusted(chain: Array<out X509Certificate>?, authType: String?) {}
        override fun checkServerTrusted(chain: Array<out X509Certificate>?, authType: String?) {}
        override fun getAcceptedIssuers(): Array<X509Certificate>? = null
    }

    val host = "localhost"
    val port = 21
    val username = "user"
    val password = "password"
    val factory = DefaultFtpsSessionFactory().apply {
        this.setHost(host)
        this.setPort(port)
        this.setUsername(username)
        this.setPassword(password)
        this.setImplicit(false)
        this.setClientMode(2)
        this.setTrustManager(trustAllCertificates)

    }
    val session = factory.session

    session.use {
        // 3) List remote directory
        val remoteFiles = it.list("/data")
        println("Found ${remoteFiles.size} entries in /data:")
        remoteFiles.forEach { file ->
            println(" - ${file.name}, dir? ${file.isDirectory}, size: ${file.size}")
        }
    }
}

fun ftpsListingFlow(ftpsSessionFactory: DefaultFtpsSessionFactory) =
    IntegrationFlow
        // Supply the remote folder name as a message payload every 60 seconds
        .fromSupplier(
            { "/data" },
            { spec -> spec.poller(Pollers.fixedDelay(5_000)) }
        )
        // Perform an LS command (list files) on the remote folder
        .handle(
            Ftp.outboundGateway(
                ftpsSessionFactory,
                AbstractRemoteFileOutboundGateway.Command.LS,
                "payload"
            ).options(AbstractRemoteFileOutboundGateway.Option.RECURSIVE) // enable recursive listing
        )
        // Handle the list of remote files
        .handle(GenericHandler<Any> { payload, _ ->
            val listing = payload as? List<*> ?: emptyList<Any>()
            listing.forEach {
                println("Remote file entry: $it")
            }
            null
        })
        .get()

fun sftpPrintFiles() {
    val host = "localhost"
    val port = 2222
    val username = "user"
    val password = "password"
    val factory = DefaultSftpSessionFactory(true)
    factory.setHost(host)
    factory.setPort(port)
    factory.setUser(username)
    factory.setPassword(password)

    // Set this to 'true' if you want to skip known_hosts checking (for testing):
    factory.setAllowUnknownKeys(true)
    val template = SftpRemoteFileTemplate(factory)
    val remoteDir = "/home/<USER>/data"
    val files = sftpListFilesRecursively(remoteDir, template)
    println(files)
}

fun sftpListFilesRecursively(remoteDir: String, template: SftpRemoteFileTemplate): List<String> {
    val allFiles = mutableListOf<String>()

    template.execute { session ->
        fun recurse(path: String) {
            val entries = session.list(path)
            entries.forEach { entry ->
                // Construct the full path
                val filePath =
                    if (path.endsWith("/")) "$path${entry.filename}"
                    else "$path/${entry.filename}"

                // Skip hidden directories (like . or ..) and hidden files
                if (entry.attributes.isDirectory && !entry.filename.startsWith(".")) {
                    recurse(filePath) // Recur into subdirectory
                } else if (!entry.attributes.isDirectory) {
                    allFiles.add(filePath) // It's a file
                }
            }
        }

        // Kick off recursion from the specified directory
        recurse(remoteDir)
    }

    return allFiles
}
*/

fun testJavaThreads() {
    Executors.newThreadPerTaskExecutor { Thread() }.use { executor ->
        val startTime = System.nanoTime()
        for (j in 0..9999) {
            val square = j
            executor.submit(
                Runnable {
                    try {
                        Thread.sleep(1000)
                        square * square
                    } catch (e: InterruptedException) {
                        Thread.currentThread().interrupt()
                    }
                }
            )
        }
        val elapsedTime = System.nanoTime() - startTime
        println("Thread süre (milisaniye): " + TimeUnit.NANOSECONDS.toMillis(elapsedTime))
    }
}

fun testVirtualThreads() {
    val startTime = System.nanoTime()
    for (j in 0..9999) {
        val square = j
        Thread.ofVirtual().start {
            try {
                Thread.sleep(9999)
                square * square
            } catch (e: InterruptedException) {
                Thread.currentThread().interrupt()
            }
        }
    }
    val elapsedTime = System.nanoTime() - startTime
    println("Virtual süre (milisaniye): " + TimeUnit.NANOSECONDS.toMillis(elapsedTime))
}
