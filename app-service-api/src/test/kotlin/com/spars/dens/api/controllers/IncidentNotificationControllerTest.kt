package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.notification.CreateDataSourceIncidentNotificationTargetDto
import com.spars.dens.api.model.notification.DataSourceIncidentNotificationTargetDto
import com.spars.dens.api.model.notification.DataSourceIncidentNotificationTargetsResponseDto
import com.spars.dens.api.model.notification.WebhookCredentialsDto
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.folder.models.FileSystemProtocol
import com.spars.dens.core.datamodel.notification.models.DataSourceIncidentNotificationTarget
import com.spars.dens.core.datamodel.notification.models.NotificationType
import com.spars.dens.core.datamodel.notification.repositories.DataSourceIncidentNotificationTargetRepository
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

class IncidentNotificationControllerTest : BaseControllerTest() {

    @Autowired
    private lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    private lateinit var dataSourceIncidentNotificationTargetRepository: DataSourceIncidentNotificationTargetRepository

    private val dataSourceFixture = DataSourceFixture(

        dataSourceType = DataSourceType.FOLDER,
        connectionSettings = ConnectionSettings.FolderConnectionSettings(
            protocol = FileSystemProtocol.SFTP,
            host = "localhost",
            port = 2222,
            userName = "user",
            password = "password"
        )
    )

    @BeforeEach
    override fun setup() {
        super.setup()
        // Create a test data source
        dataSourceRepository.create(
            tenantId = dataSourceFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )
    }

    @Test
    @WithMockUser
    fun `should list all notification targets`() {
        // Create a test notification target
        val createData = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = dataSourceFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.EMAIL,
            targetAddress = "<EMAIL>"
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = dataSourceFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(createData)
        )

        // Perform the request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.get("/v1/incident-notifications")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, dataSourceFixture.tenantId.value)
                .contentType(MediaType.APPLICATION_JSON)
        )
            .andExpect(MockMvcResultMatchers.status().isOk)
            .andReturn()

        // Parse the response
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<DataSourceIncidentNotificationTargetsResponseDto>>() {}
        )

        // Verify the response
        assertThat(response.data).isNotNull
        assertThat(response.data!!.targets).isNotEmpty
        assertThat(response.data!!.targets[0].notificationType).isEqualTo(NotificationType.EMAIL)
        assertThat(response.data!!.targets[0].targetAddress).isEqualTo("<EMAIL>")
        assertThat(response.data!!.targets[0].dataSource.dataSourceId).isEqualTo(dataSourceFixture.dataSourceId.value)
    }

    @Test
    @WithMockUser
    fun `should create a new notification target`() {
        // Create request DTO
        val createDto = CreateDataSourceIncidentNotificationTargetDto(
            dataSourceId = dataSourceFixture.dataSourceId.value,
            notificationType = NotificationType.SMS,
            targetAddress = "+1234567890"
        )

        // Perform the request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/incident-notifications")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, dataSourceFixture.tenantId.value)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDto))
        )
            .andExpect(MockMvcResultMatchers.status().isCreated)
            .andReturn()

        // Parse the response
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<DataSourceIncidentNotificationTargetDto>>() {}
        )

        // Verify the response
        assertThat(response.data).isNotNull
        assertThat(response.data!!.notificationType).isEqualTo(NotificationType.SMS)
        assertThat(response.data!!.targetAddress).isEqualTo("+1234567890")
        assertThat(response.data!!.dataSource.dataSourceId).isEqualTo(dataSourceFixture.dataSourceId.value)
    }

    @Test
    @WithMockUser
    fun `should delete a notification target`() {
        // Create a test notification target
        val createData = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = dataSourceFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.EMAIL,
            targetAddress = "<EMAIL>"
        )

        val target = dataSourceIncidentNotificationTargetRepository.create(
            tenantId = dataSourceFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(createData)
        ).first()

        // Perform the delete request
        mockMvc.perform(
            MockMvcRequestBuilders.delete("/v1/incident-notifications/${target.dataSourceIncidentNotificationTargetId.value}")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, dataSourceFixture.tenantId.value)
        )
            .andExpect(MockMvcResultMatchers.status().isOk)

        // Verify the target is marked as deleted
        val deletedTarget = dataSourceIncidentNotificationTargetRepository.getByIds(
            tenantId = dataSourceFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(target.dataSourceIncidentNotificationTargetId),
            ignoreDeleted = false
        ).first()

        assertThat(deletedTarget.isDeleted).isTrue()
    }

    @Test
    @WithMockUser
    fun `should create a webhook notification target with basic auth credentials`() {
        // Create request DTO with basic auth credentials
        val credentials = WebhookCredentialsDto.BasicAuthenticationCredentialsDto(
            username = "testuser",
            password = "testpass"
        )

        val createDto = CreateDataSourceIncidentNotificationTargetDto(
            dataSourceId = dataSourceFixture.dataSourceId.value,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "https://example.com/webhook",
            credentials = credentials
        )

        // Perform the request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/incident-notifications")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, dataSourceFixture.tenantId.value)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDto))
        )
            .andExpect(MockMvcResultMatchers.status().isCreated)
            .andReturn()

        // Parse the response
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<DataSourceIncidentNotificationTargetDto>>() {}
        )

        // Verify the response
        assertThat(response.data).isNotNull
        assertThat(response.data!!.notificationType).isEqualTo(NotificationType.WEBHOOK)
        assertThat(response.data!!.targetAddress).isEqualTo("https://example.com/webhook")
        assertThat(response.data!!.credentials).isNotNull
        assertThat(response.data!!.credentials).isInstanceOf(WebhookCredentialsDto.BasicAuthenticationCredentialsDto::class.java)

        val responseCredentials = response.data!!.credentials as WebhookCredentialsDto.BasicAuthenticationCredentialsDto
        assertThat(responseCredentials.username).isEqualTo("testuser")
        // Password should be masked in response
        assertThat(responseCredentials.password).isEqualTo("********")
    }

    @Test
    @WithMockUser
    fun `should create a webhook notification target with custom header credentials`() {
        // Create request DTO with custom header credentials
        val credentials = WebhookCredentialsDto.CustomHeaderCredentialsDto(
            headerName = "X-API-Key",
            headerValue = "secret-api-key-123"
        )

        val createDto = CreateDataSourceIncidentNotificationTargetDto(
            dataSourceId = dataSourceFixture.dataSourceId.value,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "https://api.example.com/webhook",
            credentials = credentials
        )

        // Perform the request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/incident-notifications")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, dataSourceFixture.tenantId.value)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDto))
        )
            .andExpect(MockMvcResultMatchers.status().isCreated)
            .andReturn()

        // Parse the response
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<DataSourceIncidentNotificationTargetDto>>() {}
        )

        // Verify the response
        assertThat(response.data).isNotNull
        assertThat(response.data!!.notificationType).isEqualTo(NotificationType.WEBHOOK)
        assertThat(response.data!!.targetAddress).isEqualTo("https://api.example.com/webhook")
        assertThat(response.data!!.credentials).isNotNull
        assertThat(response.data!!.credentials).isInstanceOf(WebhookCredentialsDto.CustomHeaderCredentialsDto::class.java)

        val responseCredentials = response.data!!.credentials as WebhookCredentialsDto.CustomHeaderCredentialsDto
        assertThat(responseCredentials.headerName).isEqualTo("X-API-Key")
        // Header value should be masked in response
        assertThat(responseCredentials.headerValue).isEqualTo("********")
    }

    @Test
    @WithMockUser
    fun `should create a webhook notification target without credentials`() {
        // Create request DTO without credentials
        val createDto = CreateDataSourceIncidentNotificationTargetDto(
            dataSourceId = dataSourceFixture.dataSourceId.value,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "https://public.example.com/webhook",
            credentials = null
        )

        // Perform the request
        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/incident-notifications")
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, dataSourceFixture.tenantId.value)
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(createDto))
        )
            .andExpect(MockMvcResultMatchers.status().isCreated)
            .andReturn()

        // Parse the response
        val response = objectMapper.readValue(
            result.response.contentAsString,
            object : TypeReference<ApiResponse<DataSourceIncidentNotificationTargetDto>>() {}
        )

        // Verify the response
        assertThat(response.data).isNotNull
        assertThat(response.data!!.notificationType).isEqualTo(NotificationType.WEBHOOK)
        assertThat(response.data!!.targetAddress).isEqualTo("https://public.example.com/webhook")
        assertThat(response.data!!.credentials).isNull()
    }
}
