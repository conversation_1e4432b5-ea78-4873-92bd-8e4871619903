package com.spars.dens.api.controllers

import com.fasterxml.jackson.core.type.TypeReference
import com.spars.dens.api.config.RequestTenantIdInterceptor
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.dashboard.RuleExecutionMetricsResponseDto
import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.incident.models.Incident
import com.spars.dens.core.datamodel.incident.repositories.IncidentRepository
import com.spars.dens.core.datamodel.rule.fixtures.AssetRuleFixture
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import kotlinx.datetime.Clock
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours

class DashboardIncidentCountsControllerTest : BaseControllerTest() {

    @Autowired
    lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    lateinit var assetRepository: AssetRepository

    @Autowired
    lateinit var ruleRepository: RuleRepository

    @Autowired
    lateinit var incidentRepository: IncidentRepository

    private val dataSourceFixture = DataSourceFixture()
    private val tenantFixture = TenantFixture()
    private val assetFixture = AssetFixture()
    private val assetRuleFixture = AssetRuleFixture(ruleId = RuleId.from(1234567890L))
    private val fieldRuleFixture = FieldRuleFixture(ruleId = RuleId.from(2345678901L))

    @BeforeEach
    override fun setup() {
        super.setup()
        dataSourceRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )
        assetRepository.create(tenantFixture.tenantId, dslContext, assetFixture.createData)
        ruleRepository.create(
            tenantFixture.tenantId, dslContext, listOf(assetRuleFixture.createData, fieldRuleFixture.createData)
        )
    }

    @Test
    @WithMockUser
    fun `test asset level incidents with data in current period only`() {
        // Setup - create incidents in current period
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Create asset level incidents in current period
        val incident1 = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = now.minus(12.hours)
        )

        val incident2 = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = now.minus(6.hours)
        )

        incidentRepository.create(tenantFixture.tenantId, dslContext, incident1)
        incidentRepository.create(tenantFixture.tenantId, dslContext, incident2)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/asset-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(2.0)
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(0.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(100.0)
    }

    @Test
    @WithMockUser
    fun `test column level incidents with data in current period only`() {
        // Setup - create incidents in current period
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Create field level incidents in current period
        val incident1 = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = fieldRuleFixture.ruleId,
            createdAt = now.minus(12.hours)
        )

        val incident2 = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = fieldRuleFixture.ruleId,
            createdAt = now.minus(6.hours)
        )

        incidentRepository.create(tenantFixture.tenantId, dslContext, incident1)
        incidentRepository.create(tenantFixture.tenantId, dslContext, incident2)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/column-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(2.0)
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(0.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(100.0)
    }

    @Test
    @WithMockUser
    fun `test asset level incidents with data in both periods`() {
        // Setup - create incidents in current and previous periods
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now
        val previousStartTime = startTime.minus(1.days)

        // Create asset level incidents in current period
        val currentIncident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = now.minus(12.hours)
        )

        // Create asset level incidents in previous period
        val previousIncident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = previousStartTime.plus(12.hours)
        )

        incidentRepository.create(tenantFixture.tenantId, dslContext, currentIncident)
        incidentRepository.create(tenantFixture.tenantId, dslContext, previousIncident)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/asset-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(1.0)
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(1.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(0.0)
    }

    @Test
    @WithMockUser
    fun `test column level incidents with data in both periods`() {
        // Setup - create incidents in current and previous periods
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now
        val previousStartTime = startTime.minus(1.days)

        // Create field level incidents in current period
        val currentIncident1 = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = fieldRuleFixture.ruleId,
            createdAt = now.minus(12.hours)
        )

        val currentIncident2 = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = fieldRuleFixture.ruleId,
            createdAt = now.minus(6.hours)
        )

        // Create field level incident in previous period
        val previousIncident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = fieldRuleFixture.ruleId,
            createdAt = previousStartTime.plus(12.hours)
        )

        incidentRepository.create(tenantFixture.tenantId, dslContext, currentIncident1)
        incidentRepository.create(tenantFixture.tenantId, dslContext, currentIncident2)
        incidentRepository.create(tenantFixture.tenantId, dslContext, previousIncident)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/column-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(2.0)
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(1.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(100.0)
    }

    @Test
    @WithMockUser
    fun `test asset level incidents with no incidents in current period`() {
        // Setup - create incidents only in previous period
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now
        val previousStartTime = startTime.minus(1.days)

        // Create asset level incidents in previous period
        val previousIncident = Incident.CreateData(
            tenantId = tenantFixture.tenantId,
            ruleId = assetRuleFixture.ruleId,
            createdAt = previousStartTime.plus(12.hours)
        )

        incidentRepository.create(tenantFixture.tenantId, dslContext, previousIncident)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/asset-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(200)
        val response = objectMapper.readValue(
            result.response.contentAsString, object : TypeReference<ApiResponse<RuleExecutionMetricsResponseDto>>() {}
        )

        assertThat(response.data).isNotNull
        assertThat(response.data?.currentPeriodMetrics?.value).isEqualTo(0.0)
        assertThat(response.data?.previousPeriodMetrics?.value).isEqualTo(1.0)
        assertThat(response.data?.currentPeriodMetrics?.changePercentage).isEqualTo(-100.0)
    }

    @Test
    @WithMockUser
    fun `test column level incidents with invalid time range`() {
        // Setup - create request with end time before start time
        val now = Clock.System.now()
        val startTime = now
        val endTime = now.minus(1.days)

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/column-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
                .header(RequestTenantIdInterceptor.TENANT_ID_HEADER, tenantFixture.tenantId.value)
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(400)
    }

    @Test
    @WithMockUser
    fun `test asset level incidents without tenant id header`() {
        val now = Clock.System.now()
        val startTime = now.minus(1.days)
        val endTime = now

        // Execute request
        val requestBody = """
            {
                "startTime": "$startTime",
                "endTime": "$endTime"
            }
        """.trimIndent()

        val result = mockMvc.perform(
            MockMvcRequestBuilders.post("/v1/dashboard/asset-level-incidents")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestBody)
            // No tenant ID header
        ).andReturn()

        // Verify
        assertThat(result.response.status).isEqualTo(500)
    }
}
