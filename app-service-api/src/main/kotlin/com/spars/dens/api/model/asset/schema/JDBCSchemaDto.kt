package com.spars.dens.api.model.asset.schema

import com.fasterxml.jackson.annotation.JsonTypeName
import com.spars.dens.core.datamodel.asset.models.Schema

@JsonTypeName("JDBCSchemaDto")
data class JDBCSchemaDto(
    val columns: List<JDBCColumnDto>
) : SchemaDto() {
    override fun toSchema(): Schema {
        return Schema.JDBCSchema(
            columns = columns.map { it.toJDBCColumn() }
        )
    }

    companion object {
        fun from(schema: Schema.JDBCSchema): JDBCSchemaDto {
            return JDBCSchemaDto(
                columns = schema.columns.map { JDBCColumnDto.from(it) }
            )
        }
    }
}
