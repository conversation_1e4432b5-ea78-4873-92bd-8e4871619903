package com.spars.dens.api.model.asset.schema

import com.spars.dens.core.datamodel.asset.models.JDBCColumn

data class JDBCColumnDto(
    val name: String,
    val type: String,
    val nullable: Boolean,
    val size: Int? = null,
    val precision: Int? = null,
    val scale: Int? = null,
    val jdbcType: Int? = null,
    val databaseProductName: String? = null
) {
    fun toJDBCColumn(): JDBCColumn {
        return JDBCColumn(
            name = name,
            type = type,
            nullable = nullable,
            size = size,
            precision = precision,
            scale = scale,
            jdbcType = jdbcType,
            databaseProductName = databaseProductName
        )
    }

    companion object {
        fun from(jdbcColumn: JDBCColumn): JDBCColumnDto {
            return JDBCColumnDto(
                name = jdbcColumn.name,
                type = jdbcColumn.type,
                nullable = jdbcColumn.nullable,
                size = jdbcColumn.size,
                precision = jdbcColumn.precision,
                scale = jdbcColumn.scale,
                jdbcType = jdbcColumn.jdbcType,
                databaseProductName = jdbcColumn.databaseProductName
            )
        }
    }
}
