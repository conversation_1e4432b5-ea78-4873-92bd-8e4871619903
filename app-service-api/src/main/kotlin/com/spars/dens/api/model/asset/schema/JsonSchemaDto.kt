package com.spars.dens.api.model.asset.schema

import com.fasterxml.jackson.annotation.JsonTypeName
import com.fasterxml.jackson.databind.JsonNode
import com.spars.dens.core.datamodel.asset.models.Schema

@JsonTypeName("JsonSchemaDto")
data class JsonSchemaDto(
    val schema: JsonNode
) : SchemaDto() {
    override fun toSchema(): Schema {
        return Schema.JsonSchema(schema = schema)
    }

    companion object {
        fun from(schema: Schema.JsonSchema): JsonSchemaDto {
            return JsonSchemaDto(schema = schema.schema)
        }
    }
}
