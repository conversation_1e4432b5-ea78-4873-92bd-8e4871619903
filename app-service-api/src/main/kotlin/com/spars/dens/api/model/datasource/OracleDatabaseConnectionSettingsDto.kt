package com.spars.dens.api.model.datasource

import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.spars.dens.api.utils.jackson.PasswordMaskingSerializer
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings

data class OracleDatabaseConnectionSettingsDto(
    val host: String,
    val port: Int,
    val sid: String?,
    val serviceName: String?,
    val userName: String,
    @get:JsonSerialize(using = PasswordMaskingSerializer::class)
    val password: String
) : DataSourceConnectionSettingsDto() {
    override fun toDataSourceSettings(): ConnectionSettings {
        return ConnectionSettings.OracleDatabaseConnectionSettings(host, port, sid, serviceName, userName, password)
    }
}
