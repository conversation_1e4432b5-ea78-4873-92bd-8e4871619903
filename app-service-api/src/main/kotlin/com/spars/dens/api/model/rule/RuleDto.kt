package com.spars.dens.api.model.rule

import com.spars.dens.api.model.rule.validation.ValidationRuleDto
import com.spars.dens.core.datamodel.rule.models.Rule
import com.spars.dens.core.datamodel.rule.models.RuleType
import kotlinx.datetime.Instant

data class RuleDto(
    val ruleId: Long,
    val assetId: Long,
    val fieldName: String?,
    val ruleName: String?,
    val ruleType: RuleType,
    val validationRule: ValidationRuleDto,
    val alertThreshold: AlertThresholdDto?,
    val executionCron: String,
    val active: Boolean,
    val createdAt: Instant,
    val updatedAt: Instant?,
    val isDeleted: Boolean
) {
    companion object {
        fun from(rule: Rule): RuleDto {
            return RuleDto(
                ruleId = rule.ruleId.value,
                assetId = rule.assetId.value,
                fieldName = rule.fieldName,
                ruleName = rule.ruleName,
                ruleType = rule.ruleType,
                validationRule = ValidationRuleDto.from(rule.validationRule),
                alertThreshold = rule.alertThreshold?.let { AlertThresholdDto.from(it) },
                executionCron = rule.executionCron,
                active = rule.active,
                createdAt = rule.createdAt,
                updatedAt = rule.updatedAt,
                isDeleted = rule.isDeleted
            )
        }
    }
}
