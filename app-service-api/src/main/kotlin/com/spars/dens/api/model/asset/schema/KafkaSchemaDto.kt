package com.spars.dens.api.model.asset.schema

import com.fasterxml.jackson.annotation.JsonTypeName
import com.spars.dens.core.datamodel.asset.models.Schema
import com.spars.dens.core.datamodel.kafka.models.KafkaSchemaType

@JsonTypeName("KafkaSchemaDto")
data class KafkaSchemaDto(
    val schema: String,
    val subject: String?,
    val schemaType: KafkaSchemaType,
    val version: Int?,
    val id: Int?
) : SchemaDto() {
    override fun toSchema(): Schema {
        return Schema.KafkaSchema(
            schema = schema,
            subject = subject,
            schemaType = schemaType,
            version = version,
            id = id
        )
    }

    companion object {
        fun from(schema: Schema.KafkaSchema): KafkaSchemaDto {
            return KafkaSchemaDto(
                schema = schema.schema,
                subject = schema.subject,
                schemaType = schema.schemaType,
                version = schema.version,
                id = schema.id
            )
        }
    }
}
