package com.spars.dens.api.model.rule.validation

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.spars.dens.core.datamodel.rule.models.ValidationRule
import kotlin.time.DurationUnit

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(AssetFreshnessValidationRuleDto::class),
    JsonSubTypes.Type(AssetVolumeValidationRuleDto::class),
    JsonSubTypes.Type(AssetStrictSchemaValidationRuleDto::class),
    JsonSubTypes.Type(FieldEnumValidationRuleDto::class),
    JsonSubTypes.Type(FieldRangeValidationRuleDto::class),
    JsonSubTypes.Type(FieldRegexValidationRuleDto::class),
    JsonSubTypes.Type(FieldUniqueValidationRuleDto::class),
    JsonSubTypes.Type(FieldNotNullValidationRuleDto::class)
)
sealed class ValidationRuleDto {

    companion object {
        fun from(validationRule: ValidationRule): ValidationRuleDto {
            return when (validationRule) {
                is ValidationRule.AssetVolumeValidationRule -> AssetVolumeValidationRuleDto(
                    durationAmount = validationRule.durationAmount,
                    durationUnit = validationRule.durationUnit.name,
                    minRecords = validationRule.minRecords,
                    maxRecords = validationRule.maxRecords
                )
                is ValidationRule.AssetFreshnessValidationRule -> AssetFreshnessValidationRuleDto(
                    maxLagMinutes = validationRule.maxLagMinutes
                )
                is ValidationRule.AssetStrictSchemaValidationRule -> AssetStrictSchemaValidationRuleDto()
                is ValidationRule.FieldEnumValidationRule -> FieldEnumValidationRuleDto(
                    enumValues = validationRule.enumValues
                )
                is ValidationRule.FieldNotNullValidationRule -> FieldNotNullValidationRuleDto()
                is ValidationRule.FieldRangeValidationRule -> FieldRangeValidationRuleDto(
                    minValue = validationRule.minValue,
                    minInclusive = validationRule.minInclusive,
                    maxValue = validationRule.maxValue,
                    maxInclusive = validationRule.maxInclusive
                )
                is ValidationRule.FieldRegexValidationRule -> FieldRegexValidationRuleDto(
                    regexPattern = validationRule.regexPattern
                )
                is ValidationRule.FieldUniqueValidationRule -> FieldUniqueValidationRuleDto()
            }
        }
    }

    fun toValidationRule(): ValidationRule {
        return when (this) {
            is AssetFreshnessValidationRuleDto -> ValidationRule.AssetFreshnessValidationRule(
                maxLagMinutes = this.maxLagMinutes
            )
            is AssetVolumeValidationRuleDto -> ValidationRule.AssetVolumeValidationRule(
                durationAmount = this.durationAmount,
                durationUnit = DurationUnit.valueOf(this.durationUnit),
                minRecords = this.minRecords,
                maxRecords = this.maxRecords
            )
            is FieldEnumValidationRuleDto -> ValidationRule.FieldEnumValidationRule(
                enumValues = this.enumValues
            )
            is FieldNotNullValidationRuleDto -> ValidationRule.FieldNotNullValidationRule()
            is FieldRangeValidationRuleDto -> ValidationRule.FieldRangeValidationRule(
                minValue = this.minValue,
                minInclusive = this.minInclusive,
                maxValue = this.maxValue,
                maxInclusive = this.maxInclusive
            )
            is FieldRegexValidationRuleDto -> ValidationRule.FieldRegexValidationRule(regexPattern = this.regexPattern)
            is FieldUniqueValidationRuleDto -> ValidationRule.FieldUniqueValidationRule()
            is AssetStrictSchemaValidationRuleDto -> ValidationRule.AssetStrictSchemaValidationRule()
        }
    }
}
