package com.spars.dens.api.model.notification

import com.spars.dens.api.model.datasource.DataSourceLiteDto
import com.spars.dens.core.datamodel.datasource.models.DataSource
import com.spars.dens.core.datamodel.notification.models.DataSourceIncidentNotificationTarget
import com.spars.dens.core.datamodel.notification.models.NotificationType
import kotlinx.datetime.Instant

data class DataSourceIncidentNotificationTargetDto(
    val id: Long,
    val dataSource: DataSourceLiteDto,
    val notificationType: NotificationType,
    val targetAddress: String,
    val createdAt: Instant,
    val updatedAt: Instant?
) {
    companion object {
        fun from(
            target: DataSourceIncidentNotificationTarget,
            dataSource: DataSource
        ): DataSourceIncidentNotificationTargetDto {
            return DataSourceIncidentNotificationTargetDto(
                id = target.dataSourceIncidentNotificationTargetId.value,
                dataSource = DataSourceLiteDto.from(dataSource),
                notificationType = target.notificationType,
                targetAddress = target.targetAddress,
                createdAt = target.createdAt,
                updatedAt = target.updatedAt
            )
        }
    }
}

data class CreateDataSourceIncidentNotificationTargetDto(
    val dataSourceId: Long,
    val notificationType: NotificationType,
    val targetAddress: String
)

data class DataSourceIncidentNotificationTargetsResponseDto(
    val targets: List<DataSourceIncidentNotificationTargetDto>
)
