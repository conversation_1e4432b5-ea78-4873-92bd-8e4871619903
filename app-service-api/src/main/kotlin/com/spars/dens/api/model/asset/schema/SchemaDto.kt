package com.spars.dens.api.model.asset.schema

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.spars.dens.core.datamodel.asset.models.Schema

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "_type"
)
@JsonSubTypes(
    JsonSubTypes.Type(CsvSchemaDto::class),
    JsonSubTypes.Type(JsonSchemaDto::class),
    JsonSubTypes.Type(JDBCSchemaDto::class),
    JsonSubTypes.Type(KafkaSchemaDto::class)
)
sealed class SchemaDto {
    abstract fun toSchema(): Schema

    companion object {
        fun from(schema: Schema): SchemaDto {
            return when (schema) {
                is Schema.CsvSchema -> CsvSchemaDto(columns = schema.columns)
                is Schema.JsonSchema -> JsonSchemaDto(schema = schema.schema)
                is Schema.JDBCSchema -> JDBCSchemaDto.from(schema)
                is Schema.KafkaSchema -> KafkaSchemaDto.from(schema)
            }
        }
    }
}
