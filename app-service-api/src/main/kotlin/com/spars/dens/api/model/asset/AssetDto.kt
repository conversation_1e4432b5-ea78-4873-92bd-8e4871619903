package com.spars.dens.api.model.asset

import com.spars.dens.api.model.rule.RuleDto
import com.spars.dens.core.datamodel.asset.models.Asset
import com.spars.dens.core.datamodel.asset.models.AssetType
import com.spars.dens.core.utils.data.getOrHandle
import kotlinx.datetime.Instant

data class AssetDto(
    val assetId: Long,
    val assetName: String,
    val description: String?,
    val assetType: AssetType,
    val assetTypeSpecificSettings: AssetTypeSpecificSettingsDto?,
    val currentSchema: AssetSchemaVersionDto,
    val rules: List<RuleDto>,
    val createdAt: Instant,
    val updatedAt: Instant?,
    val isDeleted: Boolean
) {
    companion object {
        fun from(
            asset: Asset
        ): AssetDto {
            return AssetDto(
                assetId = asset.assetId.value,
                assetName = asset.assetName,
                description = asset.description,
                assetType = asset.assetType,
                assetTypeSpecificSettings = asset.assetTypeSpecificSettings?.let { AssetTypeSpecificSettingsDto.from(it) },
                currentSchema = AssetSchemaVersionDto.from(asset.currentSchema),
                rules = asset.rules.getOrHandle { emptyList() }.map { RuleDto.from(it) },
                createdAt = asset.createdAt,
                updatedAt = asset.updatedAt,
                isDeleted = asset.isDeleted
            )
        }
    }
}

data class AssetLiteDto(
    val assetId: Long,
    val assetName: String,
    val description: String?
) {
    companion object {
        fun from(asset: Asset): AssetLiteDto {
            return AssetLiteDto(
                assetId = asset.assetId.value,
                assetName = asset.assetName,
                description = asset.description
            )
        }
    }
}
