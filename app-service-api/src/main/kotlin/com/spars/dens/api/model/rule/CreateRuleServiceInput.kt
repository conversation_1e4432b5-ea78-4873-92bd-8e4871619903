package com.spars.dens.api.model.rule

import com.spars.dens.core.datamodel.alert.models.AlertThreshold
import com.spars.dens.core.datamodel.asset.models.AssetId
import com.spars.dens.core.datamodel.rule.models.Rule
import com.spars.dens.core.datamodel.rule.models.RuleType
import com.spars.dens.core.datamodel.rule.models.ValidationRule
import com.spars.dens.core.datamodel.tenant.models.TenantId

data class CreateRuleServiceInput(
    val tenantId: TenantId,
    val assetId: AssetId,
    val fieldName: String?,
    val ruleName: String?,
    val ruleType: RuleType,
    val validationRule: ValidationRule,
    val alertThreshold: AlertThreshold?,
    val executionCron: String,
    val active: Boolean
) {
    fun toCreateRuleData() = Rule.CreateData(
        tenantId = tenantId,
        assetId = assetId,
        fieldName = fieldName,
        ruleName = ruleName,
        ruleType = ruleType,
        validationRule = validationRule,
        alertThreshold = alertThreshold,
        executionCron = executionCron,
        active = active
    )
}

data class CreateRuleForNewAssetServiceInput(
    val tenantId: TenantId,
    val fieldName: String?,
    val ruleName: String?,
    val ruleType: RuleType,
    val validationRule: ValidationRule,
    val alertThreshold: AlertThreshold?,
    val executionCron: String,
    val active: Boolean
) {
    fun toCreateDataForNewAsset() = Rule.CreateDataForNewAsset(
        tenantId = tenantId,
        fieldName = fieldName,
        ruleName = ruleName,
        ruleType = ruleType,
        validationRule = validationRule,
        alertThreshold = alertThreshold,
        executionCron = executionCron,
        active = active
    )
}
