package com.spars.dens.api.controllers

import arrow.core.getOrElse
import com.spars.dens.api.config.RequestTenantIdService
import com.spars.dens.api.config.TenantIdRequired
import com.spars.dens.api.model.ApiResponse
import com.spars.dens.api.model.ApiResponse.Companion.unexpectedError
import com.spars.dens.api.model.ErrorCode
import com.spars.dens.api.model.ErrorResponse
import com.spars.dens.api.model.notification.CreateDataSourceIncidentNotificationTargetDto
import com.spars.dens.api.model.notification.DataSourceIncidentNotificationTargetDto
import com.spars.dens.api.model.notification.DataSourceIncidentNotificationTargetsResponseDto
import com.spars.dens.core.datamodel.datasource.models.DataSourceId
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.notification.filters.DataSourceIncidentNotificationTargetFilter
import com.spars.dens.core.datamodel.notification.models.DataSourceIncidentNotificationTarget
import com.spars.dens.core.datamodel.notification.repositories.DataSourceIncidentNotificationTargetRepository
import com.spars.dens.core.utils.logging.MonitoredFunction
import com.spars.dens.core.utils.logging.MonitoredParameter
import io.swagger.v3.oas.annotations.Operation
import org.jooq.DSLContext
import org.slf4j.Logger
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/v1/incident-notifications")
class IncidentNotificationController(
    private val dataSourceIncidentNotificationTargetRepository: DataSourceIncidentNotificationTargetRepository,
    private val dataSourceRepository: DataSourceRepository,
    private val requestTenantIdService: RequestTenantIdService,
    private val dslContext: DSLContext,
    private val logger: Logger
) {

    @Operation(summary = "Lists all incident notification targets")
    @GetMapping
    @TenantIdRequired
    @MonitoredFunction("Controller to list all incident notification targets")
    fun listAllNotificationTargets(
        @MonitoredParameter @RequestParam(required = false) dataSourceId: Long? = null
    ): ResponseEntity<ApiResponse<DataSourceIncidentNotificationTargetsResponseDto>> {
        val tenantId = requestTenantIdService.getRequestTenantId().getOrElse {
            logger.error("Error when fetching tenant ID!", it)
            return ResponseEntity.internalServerError().body(unexpectedError())
        }

        // Create filter
        val filter = DataSourceIncidentNotificationTargetFilter()

        // Add data source filter if provided
        if (dataSourceId != null) {
            filter.dataSourceId(DataSourceId.from(dataSourceId))
        }

        // Get notification targets
        val targets = dataSourceIncidentNotificationTargetRepository.filter(
            tenantId = tenantId,
            dslContext = dslContext,
            filter = filter
        )

        if (targets.isEmpty()) {
            return ResponseEntity.ok(
                ApiResponse(
                    data = DataSourceIncidentNotificationTargetsResponseDto(
                        targets = emptyList()
                    )
                )
            )
        }

        // Get data sources for the targets
        val dataSourceIds = targets.map { it.dataSourceId }.distinct()
        val dataSources = dataSourceRepository.getByIds(
            tenantId = tenantId,
            dslContext = dslContext,
            ids = dataSourceIds
        ).associateBy { it.dataSourceId }

        // Create DTOs
        val targetDtos = targets.mapNotNull { target ->
            val dataSource = dataSources[target.dataSourceId] ?: return@mapNotNull null
            DataSourceIncidentNotificationTargetDto.from(target, dataSource)
        }

        return ResponseEntity.ok(
            ApiResponse(
                data = DataSourceIncidentNotificationTargetsResponseDto(
                    targets = targetDtos
                )
            )
        )
    }

    @Operation(summary = "Creates a new incident notification target")
    @PostMapping
    @TenantIdRequired
    @MonitoredFunction("Controller to create a new incident notification target")
    fun createNotificationTarget(
        @MonitoredParameter @RequestBody createDto: CreateDataSourceIncidentNotificationTargetDto
    ): ResponseEntity<ApiResponse<DataSourceIncidentNotificationTargetDto>> {
        val tenantId = requestTenantIdService.getRequestTenantId().getOrElse {
            logger.error("Error when fetching tenant ID!", it)
            return ResponseEntity.internalServerError().body(unexpectedError())
        }

        // Verify data source exists
        val dataSourceId = DataSourceId.from(createDto.dataSourceId)
        val dataSource = dataSourceRepository.getByIds(
            tenantId = tenantId,
            dslContext = dslContext,
            ids = listOf(dataSourceId)
        ).firstOrNull() ?: return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ApiResponse(
                error = ErrorResponse(
                    code = ErrorCode.NOT_FOUND,
                    message = "Data source not found"
                )
            )
        )

        // Create notification target
        val createData = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = tenantId,
            dataSourceId = dataSourceId,
            notificationType = createDto.notificationType,
            targetAddress = createDto.targetAddress
        )

        val createdTarget = dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantId,
            dslContext = dslContext,
            dataItems = listOf(createData)
        ).first()

        return ResponseEntity.status(HttpStatus.CREATED).body(
            ApiResponse(
                data = DataSourceIncidentNotificationTargetDto.from(createdTarget, dataSource)
            )
        )
    }

    @Operation(summary = "Deletes an incident notification target")
    @DeleteMapping("/{targetId}")
    @TenantIdRequired
    @MonitoredFunction("Controller to delete an incident notification target")
    fun deleteNotificationTarget(
        @MonitoredParameter @PathVariable targetId: Long
    ): ResponseEntity<ApiResponse<Unit>> {
        val tenantId = requestTenantIdService.getRequestTenantId().getOrElse {
            logger.error("Error when fetching tenant ID!", it)
            return ResponseEntity.internalServerError().body(unexpectedError())
        }

        // Find the target
        val target = dataSourceIncidentNotificationTargetRepository.getByIds(
            tenantId = tenantId,
            dslContext = dslContext,
            ids = listOf(com.spars.dens.core.datamodel.notification.models.DataSourceIncidentNotificationTargetId.from(targetId))
        ).firstOrNull() ?: return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ApiResponse(
                error = ErrorResponse(
                    code = ErrorCode.NOT_FOUND,
                    message = "Notification target not found"
                )
            )
        )

        // Mark as deleted
        dataSourceIncidentNotificationTargetRepository.save(
            tenantId = tenantId,
            dslContext = dslContext,
            item = target.markAsDeleted()
        )

        return ResponseEntity.ok(ApiResponse(data = Unit))
    }
}
