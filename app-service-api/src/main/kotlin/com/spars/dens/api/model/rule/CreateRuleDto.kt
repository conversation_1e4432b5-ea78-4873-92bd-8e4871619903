package com.spars.dens.api.model.rule

import com.spars.dens.api.model.rule.validation.ValidationRuleDto
import com.spars.dens.core.datamodel.asset.models.AssetId
import com.spars.dens.core.datamodel.rule.models.RuleType
import com.spars.dens.core.datamodel.tenant.models.TenantId

data class CreateRuleDto(
    val ruleName: String? = null,
    val ruleType: RuleType,
    val fieldName: String?,
    val validationRule: ValidationRuleDto,
    val alertThreshold: AlertThresholdDto?,
    val executionCron: String,
    val active: Boolean
) {
    fun toCreateRuleServiceInput(
        tenantId: TenantId,
        assetId: AssetId,
    ): CreateRuleServiceInput {
        return CreateRuleServiceInput(
            tenantId = tenantId,
            assetId = assetId,
            fieldName = fieldName,
            ruleName = ruleName,
            ruleType = ruleType,
            validationRule = validationRule.toValidationRule(),
            alertThreshold = alertThreshold?.toAlertThreshold(),
            executionCron = executionCron,
            active = active
        )
    }

    fun toCreateRuleForNewAssetServiceInput(
        tenantId: TenantId,
    ): CreateRuleForNewAssetServiceInput {
        return CreateRuleForNewAssetServiceInput(
            tenantId = tenantId,
            fieldName = fieldName,
            ruleName = ruleName,
            ruleType = ruleType,
            validationRule = validationRule.toValidationRule(),
            alertThreshold = alertThreshold?.toAlertThreshold(),
            executionCron = executionCron,
            active = active
        )
    }
}
