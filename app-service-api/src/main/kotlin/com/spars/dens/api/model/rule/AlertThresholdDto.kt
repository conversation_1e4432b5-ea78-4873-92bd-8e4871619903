package com.spars.dens.api.model.rule

import com.spars.dens.core.datamodel.alert.models.AlertThreshold
import kotlin.time.DurationUnit

data class AlertThresholdDto(
    val numberOfViolatingRecords: Long,
    val durationAmount: Int,
    val durationUnit: String
) {
    companion object {
        fun from(alertThreshold: AlertThreshold): AlertThresholdDto {
            return AlertThresholdDto(
                numberOfViolatingRecords = alertThreshold.numberOfViolatingRecords,
                durationAmount = alertThreshold.durationAmount,
                durationUnit = alertThreshold.durationUnit.toString()
            )
        }
    }

    fun toAlertThreshold(): AlertThreshold {
        return AlertThreshold(
            numberOfViolatingRecords = numberOfViolatingRecords,
            durationAmount = durationAmount,
            durationUnit = DurationUnit.valueOf(durationUnit)
        )
    }
}
