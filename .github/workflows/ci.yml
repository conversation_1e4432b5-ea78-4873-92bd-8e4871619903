---
name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: self-hosted

    env:
      ENV: ci
      SPRING_PROFILES_ACTIVE: ci

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Cache Gradle packages
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: >-
            ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*',
            '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-

      - name: Make gradlew executable
        run: chmod +x ./gradlew

      - name: Create external Docker network
        run: |
          docker network create datapakt_shared_network || true

      - name: Create required directories
        run: |
          mkdir -p .postgres .oracle kafka_data keycloak_data sftp_data ftp_data

      - name: Start Docker Compose services
        run: |
          docker-compose up -d

      - name: Wait for services to be ready
        run: |
          echo "Waiting for PostgreSQL..."
          timeout 60 bash -c \
            "until docker exec postgres pg_isready -U postgres; \
            do sleep 2; done"

          echo "Creating test database..."
          docker exec postgres psql -U postgres -c "CREATE DATABASE test;" || \
            echo "Test database already exists"

          echo "Waiting for Kafka..."
          timeout 60 bash -c \
            "until docker exec kafka kafka-topics.sh \
            --bootstrap-server localhost:9092 --list; do sleep 2; done"

          echo "Waiting for RabbitMQ..."
          timeout 60 bash -c \
            "until docker exec \$(docker ps -qf \
            \"ancestor=rabbitmq:3-management\") rabbitmqctl status; \
            do sleep 2; done"

          echo "Waiting for Redis..."
          timeout 60 bash -c \
            "until docker exec \$(docker ps -qf \
            \"ancestor=redis:7-alpine\") redis-cli ping; do sleep 2; done"

          echo "Waiting for Oracle Database (this may take a few minutes)..."
          timeout 300 bash -c \
            "until docker exec oracle-free sqlplus -s \
            SYSTEM/secure_password@//localhost:1521/FREE \
            <<< \"SELECT 1 FROM DUAL;\" | grep -q \"1\"; do sleep 10; done"

          echo "All services are ready!"

      - name: Run database migrations
        run: ./gradlew flywayMigrate -PenvironmentName=ci

      - name: Generate JOOQ classes
        run: ./gradlew generateJooq -PenvironmentName=ci

      - name: Clean build
        run: ./gradlew clean -PenvironmentName=ci

      - name: Compile project
        run: ./gradlew compileKotlin compileTestKotlin -PenvironmentName=ci

      - name: Check code format (ktlint)
        run: ./gradlew ktlintCheck -PenvironmentName=ci

      - name: Build project (without tests)
        run: ./gradlew build -x test -PenvironmentName=ci

      - name: Run tests
        run: ./gradlew test -PenvironmentName=ci

      - name: Run full check (includes jacoco coverage)
        run: ./gradlew check -PenvironmentName=ci

      - name: Publish test results
        uses: EnricoMi/publish-unit-test-result-action@v2
        if: always()
        with:
          files: |
            **/build/test-results/test/*.xml

      - name: Upload test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-reports
          path: |
            **/build/reports/tests/
            **/build/reports/jacoco/

      - name: Stop Docker Compose services
        if: always()
        run: |
          docker-compose down -v
          docker network rm datapakt_shared_network || true

      - name: Clean up Docker resources
        if: always()
        run: |
          docker system prune -f
