# GitHub Actions CI Workflow

This repository includes a comprehensive CI workflow that builds, tests, and validates the codebase.

## Workflow Overview

The CI workflow (`.github/workflows/ci.yml`) performs the following steps:

1. **Environment Setup**
   - Checks out the code
   - Sets up JDK 21 (Temurin distribution)
   - Caches Gradle dependencies for faster builds

2. **Docker Services**
   - Creates the required Docker network (`datapakt_shared_network`)
   - Starts all services defined in `docker-compose.yml`:
     - PostgreSQL (with test database)
     - Oracle Database
     - Kafka
     - RabbitMQ
     - Redis
     - Keycloak
     - SFTP/FTP servers
   - Waits for all services to be ready before proceeding

3. **Database Setup**
   - Creates a test database in PostgreSQL
   - Runs Flyway migrations
   - Generates JOOQ classes

4. **Build & Test**
   - Cleans the build
   - Compiles Kotlin code (main and test)
   - Checks code formatting with ktlint
   - Builds the project (excluding tests)
   - Runs all tests
   - Runs full check including Jacoco coverage

5. **Reporting**
   - Publishes test results
   - Uploads test reports and coverage reports as artifacts

6. **Cleanup**
   - Stops Docker Compose services
   - Cleans up Docker resources

## Configuration

### Environment Variables

The workflow sets the following environment variables:
- `ENV=ci` - Used by the application to determine the environment
- `SPRING_PROFILES_ACTIVE=ci` - Activates the CI Spring profile

### Application Configuration

CI-specific configuration files have been created:
- `app-service-api/src/main/resources/application-ci.yml`
- `app-core-datamodel/src/main/resources/application-ci.yml`

These configurations:
- Use the test database (`*************************************`)
- Disable Spring Boot's Docker Compose integration
- Use smaller batch sizes and timeouts for faster test execution
- Set appropriate logging levels for CI

## Self-Hosted Runner Requirements

This workflow is configured to run on self-hosted runners. Ensure your runner has:

1. **Docker & Docker Compose**
   - Docker Engine installed and running
   - Docker Compose v2 or later
   - Sufficient disk space for container images

2. **Java Development Kit**
   - The workflow will install JDK 21 automatically
   - Ensure the runner has internet access to download JDK

3. **System Resources**
   - At least 8GB RAM (Oracle DB is memory-intensive)
   - At least 20GB free disk space
   - Multiple CPU cores recommended

## Triggers

The workflow runs on:
- Push to `main` or `develop` branches
- Pull requests targeting `main` or `develop` branches

## Service Startup Times

- **PostgreSQL**: ~10 seconds
- **Kafka**: ~30 seconds
- **RabbitMQ**: ~20 seconds
- **Redis**: ~5 seconds
- **Oracle Database**: ~3-5 minutes (first startup)

The Oracle Database container takes the longest to initialize, especially on first run. Subsequent runs are faster due to persistent volumes.

## Troubleshooting

### Common Issues

1. **Oracle Database Timeout**
   - Oracle can take up to 5 minutes to start
   - Check Docker logs: `docker logs oracle-free`
   - Ensure sufficient memory is available

2. **Network Issues**
   - The workflow creates `datapakt_shared_network`
   - If network already exists, it will be reused
   - Network is cleaned up after the workflow

3. **Port Conflicts**
   - Ensure ports 5432, 1521, 9092, 5672, 6379, 8090, 2222, 21 are available
   - Stop any local services using these ports

4. **Disk Space**
   - Docker images require significant space
   - Regular cleanup with `docker system prune` is recommended

### Debugging

To debug issues locally:

```bash
# Start services manually
docker network create datapakt_shared_network || true
docker-compose up -d

# Check service status
docker ps
docker logs <container-name>

# Run tests with CI profile
ENV=ci ./gradlew test -PenvironmentName=ci

# Cleanup
docker-compose down -v
docker network rm datapakt_shared_network || true
```

## Artifacts

The workflow uploads the following artifacts on completion:
- Test reports (`**/build/reports/tests/`)
- Jacoco coverage reports (`**/build/reports/jacoco/`)

These can be downloaded from the GitHub Actions run page for analysis.
