package com.spars.dens.core.utils.logging

import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.reflect.MethodSignature
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Aspect
@Component
class AutoLoggingAspect {
    @Suppress("TooGenericExceptionCaught")
    @Around("@annotation(com.spars.dens.core.utils.logging.MonitoredFunction)")
    fun autoLoggedExecutionInterceptor(joinPoint: ProceedingJoinPoint): Any? {
        val start = System.currentTimeMillis()
        val methodSignature = joinPoint.signature as MethodSignature
        val methodName = methodSignature.name
        val signatureParameterNames = methodSignature.parameterNames
        val signatureAnnotations = methodSignature.method.parameterAnnotations
        val annotation = methodSignature.method.getAnnotation(MonitoredFunction::class.java)
        val logger = LoggerFactory.getLogger(joinPoint.target.javaClass)
        val result = try {
            val parametersString = StringBuilder()
            if (signatureParameterNames == null || !verifyIfAnyParametersAreMonitored(signatureAnnotations)) {
                logger.info("Starting function $methodName with info: ${annotation.info}")
            } else {
                signatureParameterNames.forEachIndexed { index, paramName ->
                    val paramValue = unmaskIfMonitoredParameter(joinPoint.args[index], signatureAnnotations[index])
                    parametersString.append("$paramName=$paramValue,")
                }
                if (parametersString.isNotEmpty()) {
                    parametersString.deleteCharAt(parametersString.length - 1)
                }
                logger.info("Starting function $methodName with info: ${annotation.info}, with parameters [$parametersString]")
            }
            joinPoint.proceed()
        } catch (throwable: Throwable) {
            logger.warn("Error occurred when calling function $methodName,", throwable)
            throw throwable
        }
        val duration = System.currentTimeMillis() - start
        logger.info("The function: $methodName successfully returned. Duration: $duration ms")
        return result
    }

    private fun verifyIfAnyParametersAreMonitored(
        signatureAnnotations: Array<Array<Annotation>>
    ): Boolean {
        var areParametersMonitored = false
        signatureAnnotations.forEach { annotations ->
            if (annotations.any { it is MonitoredParameter }) {
                areParametersMonitored = true
            }
        }
        return areParametersMonitored
    }

    private fun unmaskIfMonitoredParameter(parameter: Any?, annotations: Array<Annotation>): String {
        val isMonitored = annotations.any { it is MonitoredParameter }
        return if (isMonitored) {
            parameter?.toString() ?: "null"
        } else {
            "*****"
        }
    }
}
