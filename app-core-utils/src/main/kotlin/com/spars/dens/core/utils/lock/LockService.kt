package com.spars.dens.core.utils.lock

import com.spars.dens.core.utils.lock.error.DistributedLockNotAcquiredError
import org.redisson.api.RedissonClient
import org.slf4j.Logger
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

@Service
class LockService(
    val redissonClient: RedissonClient,
    val logger: Logger,
) {
    /**
     * Execute a block with locking over a key
     * If the lock can't acquire, it will throw a DistributedLockNotAcquiredException
     *
     * @param lockKey Key where the lock will be made
     * @param block Block that should be run on the lock
     * @param T Return type of the Block
     * @return Returns the return of the executed block
     */
    fun <T> runInLockedContext(lockKey: String, leaseTimeSeconds: Long = 30L, block: () -> T): T {
        val lock = redissonClient.getLock(lockKey)
        if (lock.tryLock(leaseTimeSeconds, TimeUnit.SECONDS)) {
            logger.debug("Acquired the distributed lock for key: $lockKey")
            return try {
                block()
            } finally {
                if (lock.isHeldByCurrentThread) {
                    lock.unlock()
                    logger.debug("Released the distributed lock for key: $lockKey")
                }
            }
        } else {
            logger.warn("Can not acquire distributed lock for key: $lockKey")
            throw DistributedLockNotAcquiredError("Can not acquire distributed lock for key: $lockKey")
        }
    }
}
