package com.spars.dens.core.utils.spring

import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.stereotype.Component

@Component
class SpringContext : ApplicationContextAware {

    override fun setApplicationContext(context: ApplicationContext) {
        Companion.setContext(context)
    }

    companion object {
        private lateinit var context: ApplicationContext

        fun <T : Any?> getBean(beanClass: Class<T>): T {
            return context.getBean(beanClass)
        }

        fun setContext(ctx: ApplicationContext) {
            context = ctx
        }
    }
}
