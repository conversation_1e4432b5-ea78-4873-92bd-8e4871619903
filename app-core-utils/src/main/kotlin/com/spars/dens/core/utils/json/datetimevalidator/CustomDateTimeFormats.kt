package com.spars.dens.core.utils.json.datetimevalidator

import com.networknt.schema.ExecutionContext
import com.networknt.schema.Format
import com.networknt.schema.ValidationContext
import com.spars.dens.core.utils.datetime.DateTimeUtils
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeParseException

object CustomDateTimeFormats {
    fun all(): List<Format> {
        return getDateFormats() + getTimeFormats() + getDateTimeFormats()
    }

    /**
     * Get all date format implementations
     */
    fun getDateFormats(): List<Format> {
        return DateTimeUtils.possibleDatePatterns().map { pattern ->
            DateFormat(pattern)
        }
    }

    /**
     * Get all time format implementations
     */
    fun getTimeFormats(): List<Format> {
        return DateTimeUtils.possibleTimePatterns().map { pattern ->
            TimeFormat(pattern)
        }
    }

    /**
     * Get all date-time format implementations
     */
    fun getDateTimeFormats(): List<Format> {
        return DateTimeUtils.possibleDateTimePatterns().map { pattern ->
            DateTimeFormat(pattern)
        }
    }

    /**
     * Format implementation for date patterns
     */
    class DateFormat(private val pattern: String) : Format {
        override fun getName(): String = pattern

        override fun matches(executionContext: ExecutionContext, validationContext: ValidationContext, value: String): Boolean {
            return try {
                LocalDate.parse(value, DateTimeFormatter.ofPattern(pattern))
                true
            } catch (e: DateTimeParseException) {
                false
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * Format implementation for time patterns
     */
    class TimeFormat(private val pattern: String) : Format {
        override fun getName(): String = pattern

        override fun matches(executionContext: ExecutionContext, validationContext: ValidationContext, value: String): Boolean {
            return try {
                LocalTime.parse(value, DateTimeFormatter.ofPattern(pattern))
                true
            } catch (e: DateTimeParseException) {
                false
            } catch (e: Exception) {
                false
            }
        }
    }

    /**
     * Format implementation for date-time patterns
     */
    class DateTimeFormat(private val pattern: String) : Format {
        override fun getName(): String = pattern

        override fun matches(executionContext: ExecutionContext, validationContext: ValidationContext, value: String): Boolean {
            return try {
                LocalDateTime.parse(value, DateTimeFormatter.ofPattern(pattern))
                true
            } catch (e: DateTimeParseException) {
                false
            } catch (e: Exception) {
                false
            }
        }
    }
}
