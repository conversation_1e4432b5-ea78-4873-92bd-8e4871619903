package com.spars.dens.core.utils.logging

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.InjectionPoint
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope

@Configuration
class LoggerConfiguration {

    /**
     * Create a new bean being the logger to be used in the application. This logger
     * can then be injected in all services.
     *
     * @param injectionPoint the injection point, ie the class in which to inject the logger
     * @return an instance of the logger
     */
    @Bean
    @Scope("prototype")
    fun logger(injectionPoint: InjectionPoint): Logger {
        return LoggerFactory.getLogger(
            injectionPoint.methodParameter?.containingClass ?: Any::class.java
        )
    }
}
