package com.spars.dens.core.utils.regex.inference
enum class RunType { LETTER, DIGIT, WHITESPACE, SYMBOL, MIXED }

/** A "run" of consecutive characters all sharing the same RunType, plus the length of that run. */
data class Run(val type: RunType, val length: Int)

object RegexInferer {

    fun inferRunBasedRegex(values: List<String>): String? {
        if (values.isEmpty()) return ""

        // 1) Attempt to unify runs across all strings
        val allRunLists = getUnifiedRuns(values)
            ?: // Mismatch in run structure → fallback to a naive approach, for example:
            // ".*" to match anything
            return null

        // 2) Build the final pattern from the unified runs
        // Avoid being too verbose if there are too many runs
        return if (allRunLists.size > 10) {
            null
        } else {
            buildPatternFromRuns(allRunLists)
        }
    }

    private fun splitIntoRuns(input: String): List<Run> {
        if (input.isEmpty()) return emptyList()

        val runs = mutableListOf<Run>()

        // Determine the type of the first character
        var currentType = charToType(input[0])
        var currentLength = 1

        for (i in 1 until input.length) {
            val ch = input[i]
            val type = charToType(ch)

            if (type == currentType) {
                // Continue the current run
                currentLength++
            } else {
                // Finalize the previous run
                runs.add(Run(currentType, currentLength))
                // Start a new run
                currentType = type
                currentLength = 1
            }
        }
        // Final run
        runs.add(Run(currentType, currentLength))

        return runs
    }

    /** Classify a single char into a RunType. */
    private fun charToType(ch: Char): RunType = when {
        ch.isDigit() -> RunType.DIGIT
        ch.isLetter() -> RunType.LETTER
        ch.isWhitespace() -> RunType.WHITESPACE
        // We'll treat anything else as a SYMBOL for simplicity.
        else -> RunType.SYMBOL
    }

    /**
     * Given a list of strings, convert each to a sequence of runs, then check
     * whether all run sequences share the same number of runs and the same run types in order.
     *
     * If they do, return a list of run-lists, one per string.
     * If not, return null (meaning we can't unify them easily).
     */
    private fun getUnifiedRuns(values: List<String>): List<List<Run>>? {
        if (values.isEmpty()) return emptyList()

        val allRunLists = values.map { splitIntoRuns(it) }
        // The first run-list is our reference
        val reference = allRunLists.first().map { it.type }

        // All must have the same number of runs
        val runCount = reference.size
        if (allRunLists.any { it.size != runCount }) {
            return null
        }

        // Check each run type in each string
        for (i in 0 until runCount) {
            val refType = reference[i]
            if (allRunLists.any { it[i].type != refType }) {
                // Mismatch in the type for the i-th run
                return null
            }
        }
        return allRunLists
    }

    private fun buildPatternFromRuns(allRunLists: List<List<Run>>): String {
        // We know allRunLists have the same run structure
        val runCount = allRunLists.first().size

        val patternBuilder = StringBuilder()

        for (i in 0 until runCount) {
            val runType = allRunLists.first()[i].type
            val lengths = allRunLists.map { it[i].length }
            val minLen = lengths.minOrNull() ?: 0
            val maxLen = lengths.maxOrNull() ?: 0

            // Build a snippet for this run
            val snippet = when (runType) {
                RunType.LETTER -> "[a-zA-Z]"
                RunType.DIGIT -> "\\d"
                RunType.WHITESPACE -> "\\s"
                RunType.SYMBOL -> "[^a-zA-Z0-9\\s]"
                // If we needed a "MIXED" fallback, we’d do "." or something else
                RunType.MIXED -> "."
            }

            // Decide whether we do {min} or {min,max} or {max}
            if (minLen == maxLen) {
                // e.g., "[a-zA-Z]{5}"
                patternBuilder.append("$snippet{$minLen}")
            } else {
                // e.g., "[a-zA-Z]{3,5}"
                patternBuilder.append("$snippet{$minLen,$maxLen}")
            }
        }
        return patternBuilder.toString()
    }
}
