package com.spars.dens.core.utils.data

sealed class Prefetch<out T> {
    data class DataIsNotPrefetchedError(val className: String) : Error("list of `$className` is not prefetched")

    abstract fun isPrefetched(): Boolean
    abstract fun isNotPrefetched(): Boolean

    companion object {
        fun <T> from(value: List<T>) = Prefetched(value)
    }

    object NotPrefetched : Prefetch<Nothing>() {
        override fun isPrefetched() = false
        override fun isNotPrefetched() = true
        override fun toString() = this::class.simpleName!!
    }

    data class Prefetched<out T>(val value: List<T>) : Prefetch<T>() {
        override fun isPrefetched() = true
        override fun isNotPrefetched() = false
    }

    inline fun <T2> flatMap(f: (List<T>) -> List<T2>): Prefetch<T2> {
        return when (this) {
            is Prefetched -> f(this.value).toPrefetch()
            is NotPrefetched -> this
        }
    }
}

inline fun <reified T> Prefetch<T>.getOrHandle(default: (Prefetch.DataIsNotPrefetchedError) -> List<T>): List<T> {
    return when (this) {
        is Prefetch.Prefetched -> this.value
        is Prefetch.NotPrefetched -> default(Prefetch.DataIsNotPrefetchedError(className = T::class.simpleName!!))
    }
}

fun <T> List<T>.toPrefetch() = Prefetch.from(this)

inline fun Prefetch<Byte>.getOrHandle(default: (Prefetch.DataIsNotPrefetchedError) -> ByteArray): ByteArray {
    return when (this) {
        is Prefetch.Prefetched<Byte> -> this.value.toByteArray()
        is Prefetch.NotPrefetched -> default(Prefetch.DataIsNotPrefetchedError(className = Byte::class.simpleName!!))
    }
}

fun ByteArray.toPrefetch() = Prefetch.from(this.asList())

inline fun Prefetch<Char>.getOrHandle(default: (Prefetch.DataIsNotPrefetchedError) -> String): String {
    return when (this) {
        is Prefetch.Prefetched<Char> -> this.value.joinToString("")
        is Prefetch.NotPrefetched -> default(Prefetch.DataIsNotPrefetchedError(className = Char::class.simpleName!!))
    }
}

fun String.toPrefetch() = Prefetch.from(this.toList())
