plugins {
    id("org.springframework.boot") version Versions.springBoot
    id("io.spring.dependency-management") version Versions.springBootDependencyManagement
    kotlin("plugin.spring") version "1.9.21"
    id("com.spars.dens.kotlin-common-conventions")
}

group = "com.spars.dens"
version = "unspecified"

repositories {
    mavenCentral()
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
}

dependencies {
    implementation(kotlin("stdlib"))
    implementation("org.springframework.boot:spring-boot-starter-aop")
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-test")
    implementation("org.springframework:spring-tx")
    implementation("org.redisson:redisson-spring-boot-starter:3.27.1")
}

tasks.getByName<Test>("test") {
    useJUnitPlatform()
}

// Disable bootJar since this is a library module
tasks.getByName("bootJar") {
    enabled = false
}

// Enable plain jar for library
tasks.getByName("jar") {
    enabled = true
}
