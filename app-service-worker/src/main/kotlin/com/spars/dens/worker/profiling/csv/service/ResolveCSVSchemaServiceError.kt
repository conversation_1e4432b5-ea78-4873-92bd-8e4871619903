package com.spars.dens.worker.profiling.csv.service

sealed class ResolveCSVSchemaServiceError(
    override val message: String?,
    override val cause: Throwable? = null
) : Error(message, cause) {

    companion object {
        fun from(e: com.spars.dens.worker.profiling.csv.service.SampleCSVServiceError) =
            SampleCSVServiceError(e.message.orEmpty(), e)
    }

    data class SampleCSVServiceError(val details: String, override val cause: Throwable? = null) :
        ResolveCSVSchemaServiceError(message = "CSV sampling error: $details", cause = cause)

    data class UnexpectedError(override val cause: Throwable) :
        ResolveCSVSchemaServiceError(message = "An unexpected error occurred in CSV schema inference: ${cause.message}", cause = cause)
}
