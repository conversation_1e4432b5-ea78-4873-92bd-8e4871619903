package com.spars.dens.worker.notification

import com.spars.dens.core.datamodel.incident.models.IncidentNotificationMessage
import com.spars.dens.core.datamodel.notification.models.NotificationType
import com.spars.dens.core.datamodel.notification.repositories.DataSourceIncidentNotificationTargetRepository
import kotlinx.serialization.json.Json
import org.jooq.DSLContext
import org.slf4j.Logger
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.stereotype.Component

@Component
class WebhookNotificationConsumer(
    private val webhookService: WebhookService,
    private val dataSourceIncidentNotificationTargetRepository: DataSourceIncidentNotificationTargetRepository,
    private val dslContext: DSLContext,
    private val logger: Logger
) {

    @RabbitListener(queues = ["incident.notification.webhook"])
    fun handleIncidentNotification(message: String) {
        try {
            val incidentMessage = Json.decodeFromString<IncidentNotificationMessage>(message)

            logger.info("Webhook notification received for incident ${incidentMessage.incidentId}")

            // Get webhook notification targets for this data source
            val webhookTargets = dataSourceIncidentNotificationTargetRepository.getByDataSourceId(
                tenantId = incidentMessage.tenantId,
                dslContext = dslContext,
                dataSourceId = incidentMessage.dataSourceId
            ).filter { it.notificationType == NotificationType.WEBHOOK }

            if (webhookTargets.isEmpty()) {
                logger.info("No webhook targets found for data source ${incidentMessage.dataSourceId}")
                return
            }

            val webhookPayload = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

            webhookTargets.forEach { target ->
                try {
                    val success = webhookService.sendWebhook(
                        url = target.targetAddress,
                        payload = webhookPayload
                    )

                    if (success) {
                        logger.info("Webhook notification sent for incident ${incidentMessage.incidentId} to ${target.targetAddress}")
                    } else {
                        logger.error("Failed to send webhook notification for incident ${incidentMessage.incidentId} to ${target.targetAddress}")
                    }
                } catch (e: Exception) {
                    logger.error("Error sending webhook notification for incident ${incidentMessage.incidentId} to ${target.targetAddress}", e)
                    // Continue with other targets even if one fails
                }
            }

            logger.info("Webhook notification processing completed for incident ${incidentMessage.incidentId}. Sent to ${webhookTargets.size} targets")
        } catch (e: Exception) {
            logger.error("Error processing incident notification for webhook", e)
            throw e // Re-throw to trigger retry mechanism
        }
    }
}
