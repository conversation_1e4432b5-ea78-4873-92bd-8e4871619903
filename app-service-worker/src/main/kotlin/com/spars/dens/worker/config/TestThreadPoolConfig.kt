package com.spars.dens.worker.config

import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.core.task.SyncTaskExecutor
import org.springframework.core.task.TaskExecutor

/**
 * Test configuration for thread pools that provides synchronous executors
 * to make testing more predictable and avoid concurrency issues in tests.
 */
@Configuration
@Profile("test")
class TestThreadPoolConfig {

    @Bean("folderScanExecutor")
    fun folderScanExecutor(): TaskExecutor {
        return SyncTaskExecutor()
    }

    @Bean("dataQualityRulesExecutor")
    fun dataQualityRulesExecutor(): TaskExecutor {
        return SyncTaskExecutor()
    }

    @Bean("alertThresholdExecutor")
    fun alertThresholdExecutor(): TaskExecutor {
        return SyncTaskExecutor()
    }

    @Bean("kafkaScanExecutor")
    fun kafkaScanExecutor(): TaskExecutor {
        return SyncTaskExecutor()
    }
}
