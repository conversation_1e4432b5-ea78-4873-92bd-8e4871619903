package com.spars.dens.worker.config

import org.springframework.amqp.core.Binding
import org.springframework.amqp.core.BindingBuilder
import org.springframework.amqp.core.DirectExchange
import org.springframework.amqp.core.FanoutExchange
import org.springframework.amqp.core.Queue
import org.springframework.amqp.core.QueueBuilder
import org.springframework.amqp.rabbit.annotation.RabbitListenerConfigurer
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory
import org.springframework.amqp.rabbit.connection.ConnectionFactory
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar
import org.springframework.amqp.rabbit.retry.RejectAndDontRequeueRecoverer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.messaging.converter.MappingJackson2MessageConverter
import org.springframework.messaging.handler.annotation.support.DefaultMessageHandlerMethodFactory
import org.springframework.messaging.handler.annotation.support.MessageHandlerMethodFactory
import org.springframework.retry.interceptor.RetryOperationsInterceptor

@Configuration
class RabbitMQConfig(
    private val connectionFactory: ConnectionFactory
) : RabbitListenerConfigurer {

    companion object {
        // Queue names
        const val FOLDER_WATCH_QUEUE = "folder-watch.scan"
        const val KAFKA_WATCH_QUEUE = "kafka-watch.scan"
        const val ASSET_FRESHNESS_QUEUE = "rule-execution.asset_freshness"
        const val ASSET_VOLUME_QUEUE = "rule-execution.asset_volume"
        const val COLUMN_LEVEL_QUEUE = "rule-execution.column-level"
        const val DATA_SOURCE_ASSETS_SYNC_QUEUE = "data-source.assets_sync"

        // Incident notification queues (separate queue for each consumer)
        const val INCIDENT_EMAIL_NOTIFICATION_QUEUE = "incident.notification.email"
        const val INCIDENT_SMS_NOTIFICATION_QUEUE = "incident.notification.sms"
        const val INCIDENT_WEBHOOK_NOTIFICATION_QUEUE = "incident.notification.webhook"

        // Incident notification exchange
        const val INCIDENT_NOTIFICATION_EXCHANGE = "incident.notification.exchange"

        // DLQ names
        const val FOLDER_WATCH_DLQ = "$FOLDER_WATCH_QUEUE.dlq"
        const val KAFKA_WATCH_DLQ = "$KAFKA_WATCH_QUEUE.dlq"
        const val ASSET_FRESHNESS_DLQ = "$ASSET_FRESHNESS_QUEUE.dlq"
        const val ASSET_VOLUME_DLQ = "$ASSET_VOLUME_QUEUE.dlq"
        const val COLUMN_LEVEL_DLQ = "$COLUMN_LEVEL_QUEUE.dlq"
        const val DATA_SOURCE_ASSETS_SYNC_DLQ = "$DATA_SOURCE_ASSETS_SYNC_QUEUE.dlq"
        const val INCIDENT_EMAIL_NOTIFICATION_DLQ = "$INCIDENT_EMAIL_NOTIFICATION_QUEUE.dlq"
        const val INCIDENT_SMS_NOTIFICATION_DLQ = "$INCIDENT_SMS_NOTIFICATION_QUEUE.dlq"
        const val INCIDENT_WEBHOOK_NOTIFICATION_DLQ = "$INCIDENT_WEBHOOK_NOTIFICATION_QUEUE.dlq"

        // Exchange names
        const val DLX_EXCHANGE = "dlx.exchange"

        // Retry configuration
        const val MAX_ATTEMPTS = 3
        const val INITIAL_INTERVAL_MS = 1000L
        const val MULTIPLIER = 2.0
        const val MAX_INTERVAL_MS = 10000L
    }

    @Bean
    fun deadLetterExchange(): DirectExchange {
        return DirectExchange(DLX_EXCHANGE)
    }

    @Bean
    fun incidentNotificationExchange(): FanoutExchange {
        return FanoutExchange(INCIDENT_NOTIFICATION_EXCHANGE)
    }

    // Dead Letter Queues
    @Bean
    fun folderWatchDLQ(): Queue {
        return QueueBuilder.durable(FOLDER_WATCH_DLQ).build()
    }

    @Bean
    fun kafkaWatchDLQ(): Queue {
        return QueueBuilder.durable(KAFKA_WATCH_DLQ).build()
    }

    @Bean
    fun assetFreshnessDLQ(): Queue {
        return QueueBuilder.durable(ASSET_FRESHNESS_DLQ).build()
    }

    @Bean
    fun assetVolumeDLQ(): Queue {
        return QueueBuilder.durable(ASSET_VOLUME_DLQ).build()
    }

    @Bean
    fun columnLevelDLQ(): Queue {
        return QueueBuilder.durable(COLUMN_LEVEL_DLQ).build()
    }

    @Bean
    fun dataSourceAssetsSyncDLQ(): Queue {
        return QueueBuilder.durable(DATA_SOURCE_ASSETS_SYNC_DLQ).build()
    }

    @Bean
    fun incidentEmailNotificationDLQ(): Queue {
        return QueueBuilder.durable(INCIDENT_EMAIL_NOTIFICATION_DLQ).build()
    }

    @Bean
    fun incidentSmsNotificationDLQ(): Queue {
        return QueueBuilder.durable(INCIDENT_SMS_NOTIFICATION_DLQ).build()
    }

    @Bean
    fun incidentWebhookNotificationDLQ(): Queue {
        return QueueBuilder.durable(INCIDENT_WEBHOOK_NOTIFICATION_DLQ).build()
    }

    // DLQ Bindings
    @Bean
    fun folderWatchDLQBinding(): Binding {
        return BindingBuilder.bind(folderWatchDLQ())
            .to(deadLetterExchange())
            .with(FOLDER_WATCH_DLQ)
    }

    @Bean
    fun kafkaWatchDLQBinding(): Binding {
        return BindingBuilder.bind(kafkaWatchDLQ())
            .to(deadLetterExchange())
            .with(KAFKA_WATCH_DLQ)
    }

    @Bean
    fun assetFreshnessDLQBinding(): Binding {
        return BindingBuilder.bind(assetFreshnessDLQ())
            .to(deadLetterExchange())
            .with(ASSET_FRESHNESS_DLQ)
    }

    @Bean
    fun assetVolumeDLQBinding(): Binding {
        return BindingBuilder.bind(assetVolumeDLQ())
            .to(deadLetterExchange())
            .with(ASSET_VOLUME_DLQ)
    }

    @Bean
    fun columnLevelDLQBinding(): Binding {
        return BindingBuilder.bind(columnLevelDLQ())
            .to(deadLetterExchange())
            .with(COLUMN_LEVEL_DLQ)
    }

    @Bean
    fun dataSourceAssetsSyncDLQBinding(): Binding {
        return BindingBuilder.bind(dataSourceAssetsSyncDLQ())
            .to(deadLetterExchange())
            .with(DATA_SOURCE_ASSETS_SYNC_DLQ)
    }

    @Bean
    fun incidentEmailNotificationDLQBinding(): Binding {
        return BindingBuilder.bind(incidentEmailNotificationDLQ())
            .to(deadLetterExchange())
            .with(INCIDENT_EMAIL_NOTIFICATION_DLQ)
    }

    @Bean
    fun incidentSmsNotificationDLQBinding(): Binding {
        return BindingBuilder.bind(incidentSmsNotificationDLQ())
            .to(deadLetterExchange())
            .with(INCIDENT_SMS_NOTIFICATION_DLQ)
    }

    @Bean
    fun incidentWebhookNotificationDLQBinding(): Binding {
        return BindingBuilder.bind(incidentWebhookNotificationDLQ())
            .to(deadLetterExchange())
            .with(INCIDENT_WEBHOOK_NOTIFICATION_DLQ)
    }

    // Main Queues with DLQ configuration
    @Bean
    fun folderWatchQueue(): Queue {
        return QueueBuilder.durable(FOLDER_WATCH_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", FOLDER_WATCH_DLQ)
            .build()
    }

    // Main Queues with DLQ configuration
    @Bean
    fun kafkaWatchQueue(): Queue {
        return QueueBuilder.durable(KAFKA_WATCH_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", KAFKA_WATCH_DLQ)
            .build()
    }

    @Bean
    fun assetFreshnessQueue(): Queue {
        return QueueBuilder.durable(ASSET_FRESHNESS_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", ASSET_FRESHNESS_DLQ)
            .build()
    }

    @Bean
    fun assetVolumeQueue(): Queue {
        return QueueBuilder.durable(ASSET_VOLUME_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", ASSET_VOLUME_DLQ)
            .build()
    }

    @Bean
    fun columnLevelQueue(): Queue {
        return QueueBuilder.durable(COLUMN_LEVEL_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", COLUMN_LEVEL_DLQ)
            .build()
    }

    @Bean
    fun dataSourceAssetsSyncQueue(): Queue {
        return QueueBuilder.durable(DATA_SOURCE_ASSETS_SYNC_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", DATA_SOURCE_ASSETS_SYNC_DLQ)
            .build()
    }

    @Bean
    fun incidentEmailNotificationQueue(): Queue {
        return QueueBuilder.durable(INCIDENT_EMAIL_NOTIFICATION_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", INCIDENT_EMAIL_NOTIFICATION_DLQ)
            .build()
    }

    @Bean
    fun incidentSmsNotificationQueue(): Queue {
        return QueueBuilder.durable(INCIDENT_SMS_NOTIFICATION_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", INCIDENT_SMS_NOTIFICATION_DLQ)
            .build()
    }

    @Bean
    fun incidentWebhookNotificationQueue(): Queue {
        return QueueBuilder.durable(INCIDENT_WEBHOOK_NOTIFICATION_QUEUE)
            .withArgument("x-dead-letter-exchange", DLX_EXCHANGE)
            .withArgument("x-dead-letter-routing-key", INCIDENT_WEBHOOK_NOTIFICATION_DLQ)
            .build()
    }

    // Bindings from fanout exchange to notification queues
    @Bean
    fun incidentEmailNotificationBinding(): Binding {
        return BindingBuilder.bind(incidentEmailNotificationQueue())
            .to(incidentNotificationExchange())
    }

    @Bean
    fun incidentSmsNotificationBinding(): Binding {
        return BindingBuilder.bind(incidentSmsNotificationQueue())
            .to(incidentNotificationExchange())
    }

    @Bean
    fun incidentWebhookNotificationBinding(): Binding {
        return BindingBuilder.bind(incidentWebhookNotificationQueue())
            .to(incidentNotificationExchange())
    }

    @Bean
    fun ruleExecutionQueues(): List<Queue> {
        return listOf(
            assetFreshnessQueue(),
            assetVolumeQueue(),
            columnLevelQueue(),
            dataSourceAssetsSyncQueue()
        )
    }

    @Bean
    fun retryOperationsInterceptor(): RetryOperationsInterceptor {
        return RetryInterceptorBuilder.stateless()
            .maxAttempts(MAX_ATTEMPTS)
            .backOffOptions(INITIAL_INTERVAL_MS, MULTIPLIER, MAX_INTERVAL_MS)
            .recoverer(RejectAndDontRequeueRecoverer()) // Send to DLQ after max attempts
            .build()
    }

    @Bean
    fun messageHandlerMethodFactory(): MessageHandlerMethodFactory {
        val factory = DefaultMessageHandlerMethodFactory()
        factory.setMessageConverter(MappingJackson2MessageConverter())
        return factory
    }

    @Bean
    fun rabbitListenerContainerFactory(): SimpleRabbitListenerContainerFactory {
        val factory = SimpleRabbitListenerContainerFactory()
        factory.setConnectionFactory(connectionFactory)
        factory.setAdviceChain(retryOperationsInterceptor())
        return factory
    }

    override fun configureRabbitListeners(registrar: RabbitListenerEndpointRegistrar) {
        registrar.messageHandlerMethodFactory = messageHandlerMethodFactory()
        registrar.setContainerFactory(rabbitListenerContainerFactory())
    }
}
