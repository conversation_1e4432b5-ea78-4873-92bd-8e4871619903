package com.spars.dens.worker.chunk

import com.spars.dens.core.datamodel.asset.models.Asset
import com.spars.dens.core.datamodel.asset.models.AssetId
import com.spars.dens.core.datamodel.asset.models.AssetTypeSpecificSettings
import com.spars.dens.core.datamodel.file.filters.AssetDataChunkInProcessFilter
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcess
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcessId
import com.spars.dens.core.datamodel.file.repositories.AssetDataChunkInProcessRepository
import com.spars.dens.core.datamodel.process.ProcessStatus
import com.spars.dens.core.datamodel.tenant.models.TenantId
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import org.jooq.DSLContext
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import kotlin.time.Duration.Companion.seconds

// Use AssetType enum values as qualifiers in ChunkGenerator
@Component
class DataChunkToProcessGenerator(
    private val assetDataChunkInProcessRepository: AssetDataChunkInProcessRepository,
    private val dslContext: DSLContext,
    private val logger: Logger,
    @Value("\${job.timeout-seconds-in-queue:300}") private val timeoutSecondsInQueue: Long,
    @Value("\${job.timeout-seconds-in-progress:600}") private val timeoutSecondsInProgress: Long,
    @Value("\${job.max-retries:3}") private val maxJobRetries: Int
) : ChunkGenerator {

    override fun generateChunks(asset: Asset): List<AssetDataChunkInProcessId> {
        logger.debug("Generating chunks for asset: {}", asset.assetId)

        val now = Clock.System.now()
        val queueTimeoutThreshold = now.minus(timeoutSecondsInQueue.seconds)
        val progressTimeoutThreshold = now.minus(timeoutSecondsInProgress.seconds)

        // Get baseInstant from asset settings if available
        val baseInstant = (asset.assetTypeSpecificSettings as? AssetTypeSpecificSettings.DataFolderAssetSettings)?.baseInstant

        // Get all NEW data chunks
        val newChunks = getNewDataChunksToProcess(
            tenantId = asset.tenantId,
            assetId = asset.assetId,
            baseInstant = baseInstant
        )
        logger.debug("Found {} new data chunks for asset {}", newChunks.size, asset.assetId)

        // Get QUEUED data chunks that have timed out
        val timedOutQueuedDataChunks = getDataChunksByStatusWithTimeout(
            tenantId = asset.tenantId,
            assetId = asset.assetId,
            status = ProcessStatus.QUEUED,
            timeoutThreshold = queueTimeoutThreshold,
            baseInstant = baseInstant
        )
        logger.debug("Found {} timed out queued data chunks for asset {}", timedOutQueuedDataChunks.size, asset.assetId)

        // Get IN_PROGRESS data chunks that have timed out
        val timedOutInProgressDataChunks = getDataChunksByStatusWithTimeout(
            tenantId = asset.tenantId,
            assetId = asset.assetId,
            status = ProcessStatus.IN_PROGRESS,
            timeoutThreshold = progressTimeoutThreshold,
            baseInstant = baseInstant
        )
        logger.debug("Found {} timed out in progress data chunks for asset {}", timedOutInProgressDataChunks.size, asset.assetId)

        // Get FAILED file logs with retry attempts remaining
        val retriableFailedDataChunks = getDataChunkFilesFailedWithRetryAttempts(
            tenantId = asset.tenantId,
            assetId = asset.assetId,
            baseInstant = baseInstant
        )
        logger.debug("Found {} retriable failed data chunks for asset {}", retriableFailedDataChunks.size, asset.assetId)

        // Combine all eligible file logs
        val allEligibleDataChunks = newChunks + timedOutQueuedDataChunks +
            timedOutInProgressDataChunks + retriableFailedDataChunks

        // Convert to FileLogDataChunk objects
        return allEligibleDataChunks.map { assetDataChunkInProcess ->
            assetDataChunkInProcess.assetDataChunkInProcessId
        }
    }

    private fun getNewDataChunksToProcess(
        tenantId: TenantId,
        assetId: AssetId,
        baseInstant: Instant?
    ): List<AssetDataChunkInProcess> {
        var filter = AssetDataChunkInProcessFilter()
            .assetId(assetId)
            .processedStatusIn(listOf(ProcessStatus.NEW))

        // Add created after filter if baseInstant is provided
        if (baseInstant != null) {
            filter = filter.createdAfter(baseInstant)
        }

        return assetDataChunkInProcessRepository.filter(
            tenantId = tenantId,
            dslContext = dslContext,
            filter = filter
        )
    }

    private fun getDataChunksByStatusWithTimeout(
        tenantId: TenantId,
        assetId: AssetId,
        status: ProcessStatus,
        timeoutThreshold: Instant,
        baseInstant: Instant?
    ): List<AssetDataChunkInProcess> {
        var filter = AssetDataChunkInProcessFilter()
            .assetId(assetId)
            .processedStatusIn(listOf(status))

        // Add created after filter if baseInstant is provided
        if (baseInstant != null) {
            filter = filter.createdAfter(baseInstant)
        }

        val fileLogs = assetDataChunkInProcessRepository.filter(
            tenantId = tenantId,
            dslContext = dslContext,
            filter = filter
        )

        return fileLogs.filter { fileLog ->
            fileLog.updatedAt?.let { updatedAt ->
                updatedAt < timeoutThreshold && fileLog.numTrials < maxJobRetries
            } ?: false
        }
    }

    private fun getDataChunkFilesFailedWithRetryAttempts(
        tenantId: TenantId,
        assetId: AssetId,
        baseInstant: Instant?
    ): List<AssetDataChunkInProcess> {
        var filter = AssetDataChunkInProcessFilter()
            .assetId(assetId)
            .processedStatusIn(listOf(ProcessStatus.FAILED))

        // Add created after filter if baseInstant is provided
        if (baseInstant != null) {
            filter = filter.createdAfter(baseInstant)
        }

        val fileLogs = assetDataChunkInProcessRepository.filter(
            tenantId = tenantId,
            dslContext = dslContext,
            filter = filter
        )

        return fileLogs.filter { fileLog ->
            fileLog.numTrials < maxJobRetries
        }
    }
}
