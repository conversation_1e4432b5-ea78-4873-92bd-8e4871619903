package com.spars.dens.worker.profiling.csv.service

import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.spars.dens.core.datamodel.asset.models.FieldType
import com.spars.dens.core.datamodel.rule.models.ValidationRule
import com.spars.dens.core.utils.logging.MonitoredFunction
import com.spars.dens.core.utils.logging.MonitoredParameter
import com.spars.dens.worker.profiling.common.model.ResolveCsvSchemaResult
import com.spars.dens.worker.profiling.common.model.ResolvedFieldSchema
import com.spars.dens.worker.profiling.common.services.inference.rule.EnumRuleChecker
import com.spars.dens.worker.profiling.common.services.inference.rule.regex.HighLevelRegexRuleChecker
import com.spars.dens.worker.profiling.common.services.inference.type.FieldTypeResolver
import com.spars.dens.worker.profiling.csv.model.ResolveCsvSchemaServiceInput
import org.slf4j.Logger
import org.springframework.stereotype.Service

@Service
class ResolveCSVSchemaService(
    private val sampleCSVService: SampleCSVService,
    private val typeCheckers: List<FieldTypeResolver>,
    private val highLevelRegexRuleCheckers: List<HighLevelRegexRuleChecker>,
    private val enumRuleChecker: EnumRuleChecker,
    private val logger: Logger
) {

    @MonitoredFunction("Service to resolve CSV schema")
    fun resolveSchema(
        @MonitoredParameter input: ResolveCsvSchemaServiceInput
    ): Either<ResolveCSVSchemaServiceError, ResolveCsvSchemaResult> {
        try {
            // 1. Sample rows from files
            val (columnNames, allRows) = sampleCSVService.sampleFiles(
                files = input.files,
                delimiter = input.delimiter,
                hasHeaderInFirstLine = input.hasHeaderInFirstLine,
                compressed = input.compressed
            ).getOrElse {
                return ResolveCSVSchemaServiceError.from(it).left()
            }

            // 2. Transpose rows => columns
            val columns = transpose(allRows)
            // 3. Infer column schema
            val columnSchemas = columns.mapIndexed { index, columnValues ->
                resolveColumnSchema(columnNames[index], columnValues)
            }
            return ResolveCsvSchemaResult(
                fieldSchemas = columnSchemas,
            ).right()
        } catch (e: Exception) {
            return ResolveCSVSchemaServiceError.UnexpectedError(e).left()
        }
    }

    private fun resolveRegexRulesByName(values: List<String>): List<Pair<String?, ValidationRule.FieldRegexValidationRule>> {
        // Apply each rule in order
        val nonBlankValues = values.filter { it.isNotBlank() }
        for (rule in highLevelRegexRuleCheckers) {
            val resolvedRule = rule.check(nonBlankValues)
            if (resolvedRule != null) {
                return listOf(resolvedRule)
            }
        }
        return emptyList()
    }

    private fun resolveColumnSchema(columnName: String, values: List<String>): ResolvedFieldSchema {
        val totalCount = values.size
        val nonBlankValues = values.filter { it.isNotBlank() }
        val nullCount = totalCount - nonBlankValues.size
        val isNullable = (nullCount.toDouble() / totalCount) > 0.01

        // Apply each type resolver in order
        for (typeResolver in typeCheckers.sortedBy { it.rank() }) {
            val resolvedType = typeResolver.resolve(nonBlankValues)
            if (resolvedType != null) {
                val resolvedRulesByName = mutableListOf<Pair<String?, ValidationRule>>()
                val matchingEnumRule = enumRuleChecker.check(nonBlankValues)
                if (matchingEnumRule != null) {
                    resolvedRulesByName.add(null to matchingEnumRule)
                }
                val matchingRegexRules = if (matchingEnumRule != null || resolvedType.type != FieldType.NUMBER) {
                    emptyList()
                } else {
                    resolveRegexRulesByName(nonBlankValues)
                }
                resolvedRulesByName.addAll(matchingRegexRules.map { it.first to it.second })
                if (!isNullable) {
                    resolvedRulesByName.add("$columnName not null" to ValidationRule.FieldNotNullValidationRule())
                }
                return ResolvedFieldSchema(
                    name = columnName,
                    nullable = isNullable,
                    type = resolvedType.type,
                    format = resolvedType.format,
                    resolvedRulesByName = resolvedRulesByName
                )
            }
        }
        val resolvedRulesByName = mutableListOf<Pair<String?, ValidationRule>>()
        val matchingEnumRule = enumRuleChecker.check(nonBlankValues)
        if (matchingEnumRule != null) {
            resolvedRulesByName.add(null to matchingEnumRule)
        }
        val matchingRegexRules = if (matchingEnumRule != null) {
            emptyList()
        } else {
            resolveRegexRulesByName(nonBlankValues)
        }
        resolvedRulesByName.addAll(matchingRegexRules.map { it.first to it.second })
        if (!isNullable) {
            resolvedRulesByName.add("$columnName not null" to ValidationRule.FieldNotNullValidationRule())
        }
        return ResolvedFieldSchema(
            columnName,
            isNullable,
            FieldType.TEXT,
            format = null,
            resolvedRulesByName = resolvedRulesByName
        )
    }

    private fun transpose(rows: List<List<String>>): List<List<String>> {
        if (rows.isEmpty()) return emptyList()
        val numCols = rows.first().size
        val transposed = List(numCols) { mutableListOf<String>() }
        for (row in rows) {
            for ((colIndex, value) in row.withIndex()) {
                transposed[colIndex].add(value)
            }
        }
        return transposed
    }
}
