package com.spars.dens.worker.config

import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.worker.datasource.service.HealthCheckDataSourceService
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class HealthCheckConfiguration(
    private val healthCheckServices: List<HealthCheckDataSourceService>
) {
    @Bean
    fun healthCheckDataSourceServiceMap(): Map<DataSourceType, HealthCheckDataSourceService> {
        return healthCheckServices.associateBy { it.dataSourceType() }
    }
}
