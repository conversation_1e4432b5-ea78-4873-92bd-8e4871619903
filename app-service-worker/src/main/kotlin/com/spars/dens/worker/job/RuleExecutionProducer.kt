package com.spars.dens.worker.job

import arrow.core.Either
import com.cronutils.model.CronType
import com.cronutils.model.definition.CronDefinitionBuilder
import com.cronutils.model.time.ExecutionTime
import com.cronutils.parser.CronParser
import com.spars.dens.core.datamodel.asset.models.Asset
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcessId
import com.spars.dens.core.datamodel.file.repositories.AssetDataChunkInProcessRepository
import com.spars.dens.core.datamodel.rule.filters.RuleFilter
import com.spars.dens.core.datamodel.rule.models.AssetLevelRuleMessage
import com.spars.dens.core.datamodel.rule.models.ColumnLevelRulesMessage
import com.spars.dens.core.datamodel.rule.models.RuleLevel
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.tenant.repository.TenantRepository
import com.spars.dens.worker.chunk.ChunkGenerator
import kotlinx.datetime.toKotlinInstant
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jooq.DSLContext
import org.redisson.api.RedissonClient
import org.slf4j.Logger
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.context.annotation.Profile
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.time.ZonedDateTime
import java.util.concurrent.TimeUnit

@Component
@Profile("!test")
class RuleExecutionProducer(
    private val dslContext: DSLContext,
    private val assetRepository: AssetRepository,
    private val assetDataChunkInProcessRepository: AssetDataChunkInProcessRepository,
    private val ruleRepository: RuleRepository,
    private val tenantRepository: TenantRepository,
    private val dataChunkToProcessGenerator: ChunkGenerator,
    private val rabbitTemplate: RabbitTemplate,
    private val redisson: RedissonClient,
    private val logger: Logger
) {
    private val cronParser = CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.SPRING))

    @Scheduled(cron = "0 * * * * *") // Runs at the start of every minute
    @Transactional
    fun produceRulesToExecute() {
        val lock = redisson.getLock("rule_execution_scheduler_lock")

        try {
            // Try to acquire lock for 10 seconds, hold it for 1 minute max
            if (lock.tryLock(10, 60, TimeUnit.SECONDS)) {
                try {
                    produceRulesToExecuteWithLock()
                } finally {
                    if (lock.isHeldByCurrentThread) {
                        lock.unlock()
                    }
                }
            } else {
                logger.debug("Failed to acquire lock for rule execution scheduler")
            }
        } catch (e: InterruptedException) {
            logger.error("Interrupted while trying to acquire lock", e)
            Thread.currentThread().interrupt()
        }
    }

    private fun produceRulesToExecuteWithLock() {
        Either.catch {
            val now = ZonedDateTime.now()
            val activeTenants = tenantRepository.fetchAll(dslContext)
            for (tenant in activeTenants) {
                try {
                    val activeRules = ruleRepository.filter(
                        tenantId = tenant.tenantId,
                        dslContext = dslContext,
                        filter = RuleFilter().active().assetNotDeleted(),
                        ignoreDeleted = true
                    )

                    val allDueRules = activeRules.filter { rule ->
                        try {
                            // Try to parse with Spring format (6 parts)
                            val cronExpression = ensureValidCronExpression(rule.executionCron)
                            val cron = cronParser.parse(cronExpression)
                            val executionTime = ExecutionTime.forCron(cron)
                            val lastExecution = executionTime.lastExecution(now).get()
                            val nextExecution = executionTime.nextExecution(now).get()
                            val toleranceSeconds = 15L
                            val isWithinWindow = Duration.between(lastExecution, now).abs().seconds <= toleranceSeconds ||
                                Duration.between(now, nextExecution).abs().seconds <= toleranceSeconds
                            isWithinWindow
                        } catch (e: Exception) {
                            logger.error("Invalid cron expression for rule ${rule.ruleId}: ${rule.executionCron}")
                            false
                        }
                    }
                    logger.info("Found ${allDueRules.size} rules due for execution: ${allDueRules.map { it.ruleId }}")
                    val allAssetsById = assetRepository.getByIds(
                        tenantId = tenant.tenantId,
                        dslContext = dslContext,
                        ids = allDueRules.map { it.assetId }.distinct(),
                        ignoreDeleted = true,
                        prefetchRules = false
                    ).associateBy { it.assetId }
                    // Rest of the method remains unchanged
                    val groupedRules = allDueRules.groupBy { it.assetId }

                    groupedRules.forEach { (assetId, rules) ->
                        val asset = allAssetsById[assetId] ?: run {
                            logger.error("Asset not found for tenant ${tenant.tenantId} and asset $assetId")
                            return@forEach
                        }
                        // Split rules by level
                        val (assetLevelRules, columnLevelRules) = rules.partition {
                            it.validationRule.level == RuleLevel.ASSET
                        }

                        // Process asset-level rules
                        assetLevelRules.forEach { rule ->
                            val message = AssetLevelRuleMessage(
                                tenantId = tenant.tenantId,
                                assetId = assetId,
                                ruleId = rule.ruleId,
                                plannedStartTime = now.toInstant().toString()
                            )

                            val queueName = "rule-execution.${rule.ruleType.name.lowercase()}"
                            rabbitTemplate.convertAndSend(queueName, Json.encodeToString(message))
                            logger.info("Sent asset-level rule to queue $queueName: $message")
                        }

                        // Process column-level rules as a batch
                        if (columnLevelRules.isNotEmpty()) {
                            val messages = if (asset.assetType.isStreamingByDefault) {
                                val chunkIds = dataChunkToProcessGenerator.generateChunks(asset)
                                val messages = chunkIds.map { chunk ->
                                    ColumnLevelRulesMessage(
                                        tenantId = tenant.tenantId,
                                        assetId = assetId,
                                        ruleIds = columnLevelRules.map { it.ruleId },
                                        plannedStartTime = now.toInstant().toKotlinInstant(),
                                        dataChunkId = chunk
                                    )
                                }
                                logger.debug("Generated {} chunks to process for asset {}", chunkIds.size, assetId)
                                messages
                            } else {
                                logger.debug("No chunks to process for asset {}. Sending a single message.", assetId)
                                listOf(
                                    ColumnLevelRulesMessage(
                                        tenantId = tenant.tenantId,
                                        assetId = assetId,
                                        ruleIds = columnLevelRules.map { it.ruleId },
                                        plannedStartTime = now.toInstant().toKotlinInstant(),
                                        dataChunkId = null
                                    )
                                )
                            }
                            if (asset.assetType.isStreamingByDefault && messages.isNotEmpty()) {
                                val chunkIds = messages.mapNotNull { it.dataChunkId }
                                markChunksAsQueued(
                                    asset = asset,
                                    chunkIds = chunkIds
                                )
                            }
                            messages.forEach { message ->
                                logger.info("Sending column-level rules message: {}", message)
                                rabbitTemplate.convertAndSend("rule-execution.column-level", Json.encodeToString(message))
                            }
                            logger.info("Sent ${messages.size} column-level rule execution messages")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error producing rule messages for tenant ${tenant.tenantId}", e)
                }
            }
        }.mapLeft {
            logger.error("Error in rule execution scheduler", it)
        }
    }

    /**
     * Ensures the cron expression is in the correct format for Spring's cron parser.
     * Converts 5-part Unix cron expressions to 6-part Spring cron expressions by adding
     * a '0' for the seconds field if needed.
     */
    private fun ensureValidCronExpression(cronExpression: String): String {
        // If it's a 5-part expression (standard Unix format), add '0' for seconds
        val parts = cronExpression.trim().split("\\s+".toRegex())
        return if (parts.size == 5) {
            "0 $cronExpression"
        } else {
            cronExpression
        }
    }

    fun markChunksAsQueued(asset: Asset, chunkIds: List<AssetDataChunkInProcessId>) {
        val dataChunksInProcess = assetDataChunkInProcessRepository.getByIds(
            tenantId = asset.tenantId,
            dslContext = dslContext,
            ids = chunkIds,
            ignoreDeleted = true
        )
        assetDataChunkInProcessRepository.markAsQueued(
            tenantId = asset.tenantId,
            dslContext = dslContext,
            ids = dataChunksInProcess.map { it.assetDataChunkInProcessId }
        )
    }
}
