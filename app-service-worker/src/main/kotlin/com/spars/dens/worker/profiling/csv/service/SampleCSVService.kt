package com.spars.dens.worker.profiling.csv.service

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.spars.dens.worker.profiling.csv.model.SampledRawData
import org.apache.commons.csv.CSVFormat
import org.apache.commons.vfs2.FileObject
import org.slf4j.Logger
import org.springframework.stereotype.Service
import java.util.zip.GZIPInputStream

@Service
class SampleCSVService(
    private val logger: Logger
) {
    fun sampleFiles(
        files: List<FileObject>,
        delimiter: String,
        hasHeaderInFirstLine: Boolean,
        compressed: Boolean,
        minLines: Int = 100,
        maxLines: Int = 50_000
    ): Either<SampleCSVServiceError, SampledRawData> {
        if (files.isEmpty()) {
            return SampleCSVServiceError.FileListEmptyError().left()
        }
        // Calculate total size of files
        val totalSize = files.sumOf { it.content.size }
        val aggregatedRows = mutableListOf<List<String>>()
        var consistentHeader: List<String>? = null
        var totalLinesRead = 0

        for (file in files) {
            val ratio = file.content.size.toDouble() / totalSize.toDouble()
            val linesToRead = (ratio * maxLines).toInt().coerceAtLeast(1)

            // Open file (with or without GZIP)
            val inputStream = if (compressed) {
                GZIPInputStream(file.content.inputStream)
            } else {
                file.content.inputStream
            }

            inputStream.buffered().use { buf ->
                // Use Apache Commons CSV for parsing
                val csvParser = CSVFormat.DEFAULT.builder()
                    .setDelimiter(delimiter) // assume single-char delimiter
                    .build()
                    .parse(buf.reader())

                var fileHeader: List<String>?
                var fileLineCount = 0

                for (record in csvParser) {
                    // If it's the first line of this file and we have headers
                    if (hasHeaderInFirstLine && fileLineCount == 0) {
                        fileHeader = record.toList()
                        // Compare with global "consistentHeader"
                        if (consistentHeader == null) {
                            consistentHeader = fileHeader
                        } else {
                            if (fileHeader.size != consistentHeader!!.size || fileHeader != consistentHeader) {
                                logger.warn("Header mismatch found in file: ${file.name.path}. Expected: $consistentHeader, found: $fileHeader")
                                return SampleCSVServiceError.HeaderMismatchAcrossFilesError(file.name.path).left()
                            }
                        }
                    } else {
                        // normal row
                        val rowValues = record.toList()
                        // If we have a known header, ensure row length matches
                        if (consistentHeader != null && rowValues.size != consistentHeader!!.size) {
                            logger.warn(
                                "Inconsistent column count found in file: ${file.name.path}." +
                                    " Expected: ${consistentHeader!!.size}, found: ${rowValues.size}"
                            )
                            return SampleCSVServiceError.InconsistentColumnCountFoundInFileError(
                                fileName = file.name.path,
                                columnsFound = rowValues.size,
                                columnsExpected = consistentHeader!!.size
                            ).left()
                        }
                        aggregatedRows.add(rowValues)
                        totalLinesRead++

                        if (totalLinesRead >= maxLines) {
                            break
                        }
                        if (fileLineCount >= linesToRead) {
                            break
                        }
                    }
                    fileLineCount++
                }
            }

            // If no header is set globally but we read some rows, generate it
            if (!hasHeaderInFirstLine && consistentHeader == null && aggregatedRows.isNotEmpty()) {
                val firstRow = aggregatedRows.first()
                consistentHeader = generateFieldNames(firstRow.size)
            }

            // If no rows were read at all from a file (maybe it’s empty), fill with empty
            if (!hasHeaderInFirstLine && aggregatedRows.isEmpty()) {
                consistentHeader = emptyList()
            }

            // If we already have a header but no data, ensure column count matches
            if (!hasHeaderInFirstLine && consistentHeader != null && aggregatedRows.isNotEmpty()) {
                for (row in aggregatedRows) {
                    if (row.size != consistentHeader!!.size) {
                        logger.warn(
                            "Inconsistent column count found. Expected: ${consistentHeader!!.size}, found: ${row.size}"
                        )
                        return SampleCSVServiceError.InconsistentColumnCountFoundError(
                            columnsFound = row.size,
                            columnsExpected = consistentHeader!!.size
                        ).left()
                    }
                }
            }

            if (totalLinesRead >= maxLines) {
                break
            }
        }
        if (totalLinesRead < minLines) {
            return SampleCSVServiceError.TooFewLinesError(linesRead = totalLinesRead, linesExpected = minLines).left()
        }

        val finalHeader = consistentHeader ?: emptyList()
        return SampledRawData(finalHeader, aggregatedRows).right()
    }

    private fun generateFieldNames(count: Int): List<String> {
        return (1..count).map { i -> "FIELD_$i" }
    }
}
