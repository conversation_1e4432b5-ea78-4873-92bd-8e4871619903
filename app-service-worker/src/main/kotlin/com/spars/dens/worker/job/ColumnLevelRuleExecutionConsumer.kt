package com.spars.dens.worker.job

import com.spars.dens.core.datamodel.asset.models.Asset
import com.spars.dens.core.datamodel.asset.models.AssetType
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.rule.models.ColumnLevelRulesMessage
import com.spars.dens.worker.dataquality.ColumnLevelRulesRunner
import kotlinx.serialization.json.Json
import org.jooq.DSLContext
import org.slf4j.Logger
import org.springframework.amqp.rabbit.annotation.RabbitListener
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.task.TaskExecutor
import org.springframework.stereotype.Component
import java.util.concurrent.Semaphore

@Component
class ColumnLevelRuleExecutionConsumer(
    private val assetRepository: AssetRepository,
    private val columnLevelRulesRunner: Map<String, ColumnLevelRulesRunner>,
    @Qualifier("dataQualityRulesExecutor")
    private val dataQualityRulesExecutor: TaskExecutor,
    private val streamingJobsConcurrencyLimiter: Semaphore,
    private val dslContext: DSLContext,
    private val logger: Logger
) {

    @RabbitListener(queues = ["rule-execution.column-level"])
    fun consumeColumnLevelRuleExecutionMessage(message: String) {
        val columnLevelRulesMessage = Json.decodeFromString<ColumnLevelRulesMessage>(message)
        logger.info(
            "Received Column Level Rule Execution set for asset: ${columnLevelRulesMessage.assetId}," +
                " planned start time: ${columnLevelRulesMessage.plannedStartTime}"
        )
        val asset = assetRepository.getByIds(
            tenantId = columnLevelRulesMessage.tenantId,
            dslContext = dslContext,
            ids = listOf(columnLevelRulesMessage.assetId),
            prefetchRules = false
        ).firstOrNull() ?: run {
            logger.error("Asset not found: ${columnLevelRulesMessage.assetId}")
            return
        }
        val ruleRunnerKey = getColumnLevelRuleRunnerKey(asset)
        val ruleRunner = columnLevelRulesRunner[ruleRunnerKey]
            ?: run {
                logger.error("No executor found for asset type: ${asset.assetType}")
                return
            }
        if (asset.assetType.isStreamingByDefault) {
            val permitAcquired = streamingJobsConcurrencyLimiter.tryAcquire()
            if (!permitAcquired) {
                logger.warn("No permits available for streaming jobs. Skipping execution for asset: ${asset.assetId}")
                throw RuntimeException("No permits available for streaming jobs")
            }
        }
        dataQualityRulesExecutor.execute {
            ruleRunner.runRules(columnLevelRulesMessage)
        }
    }

    private fun getColumnLevelRuleRunnerKey(asset: Asset): String {
        return when (asset.assetType) {
            AssetType.DATA_FOLDER -> "DATA_FOLDER-${asset.currentSchema.dataFormat}"
            AssetType.TABLE -> TODO()
            AssetType.VIEW -> TODO()
            AssetType.KAFKA_TOPIC -> "KAFKA_TOPIC"
        }
    }
}
