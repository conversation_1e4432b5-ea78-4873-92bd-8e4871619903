package com.spars.dens.worker.job

import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.incident.models.ColumnLevelRuleIncidentDetails
import com.spars.dens.core.datamodel.incident.models.Incident
import com.spars.dens.core.datamodel.incident.models.IncidentNotificationMessage
import com.spars.dens.core.datamodel.incident.repositories.IncidentRepository
import com.spars.dens.core.datamodel.notification.filters.DataSourceIncidentNotificationTargetFilter
import com.spars.dens.core.datamodel.notification.repositories.DataSourceIncidentNotificationTargetRepository
import com.spars.dens.core.datamodel.rule.filters.RuleSetExecutionFilter
import com.spars.dens.core.datamodel.rule.models.Rule
import com.spars.dens.core.datamodel.rule.repositories.RuleSetExecutionRepository
import com.spars.dens.core.datamodel.tenant.models.TenantId
import kotlinx.datetime.Instant
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jooq.DSLContext
import org.slf4j.Logger
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class IncidentChecker(
    private val dslContext: DSLContext,
    private val incidentRepository: IncidentRepository,
    private val ruleSetExecutionRepository: RuleSetExecutionRepository,
    private val assetRepository: AssetRepository,
    private val dataSourceRepository: DataSourceRepository,
    private val notificationTargetRepository: DataSourceIncidentNotificationTargetRepository,
    private val rabbitTemplate: RabbitTemplate,
    private val logger: Logger
) {

    @Transactional
    fun checkRuleTestsForIncident(
        tenantId: TenantId,
        rule: Rule,
        now: Instant
    ) {
        try {
            val threshold = rule.alertThreshold ?: return

            // Calculate the time window based on threshold duration
            val duration = threshold.duration()
            val timeWindowStartTime = now.minus(duration)

            // Get rule execution results within the time window
            val allExecutionsIncludingRule = ruleSetExecutionRepository.filter(
                tenantId = tenantId,
                dslContext = dslContext,
                filter = RuleSetExecutionFilter()
                    .createdAfterInclusive(timeWindowStartTime)
                    .includesRule(rule.ruleId)
            )

            // Count total violations for this specific rule
            val totalViolations = allExecutionsIncludingRule.sumOf { execution ->
                execution.resultDetails.singleRuleExecutionResults
                    .find { it.ruleId == rule.ruleId }
                    ?.numRowsFailed ?: 0
            }

            if (totalViolations >= threshold.numberOfViolatingRecords) {
                logger.warn(
                    "Alert threshold exceeded for rule ${rule.ruleId} (${rule.ruleName}): $totalViolations violations " +
                        "in the last ${threshold.durationAmount} ${threshold.durationUnit}"
                )

                // Create an incident record
                val incident = createIncident(
                    tenantId = tenantId,
                    rule = rule,
                    timeWindowStart = timeWindowStartTime,
                    timeWindowEnd = now,
                    thresholdViolations = threshold.numberOfViolatingRecords,
                    actualViolations = totalViolations
                )

                // Send notification message if incident was created successfully
                incident?.let {
                    sendIncidentNotification(it, timeWindowStartTime, now, threshold.numberOfViolatingRecords, totalViolations)
                }
            }
        } catch (e: Exception) {
            logger.error("Error checking incident threshold for rule ${rule.ruleId}", e)
        }
    }

    private fun createIncident(
        tenantId: TenantId,
        rule: Rule,
        timeWindowStart: Instant,
        timeWindowEnd: Instant,
        thresholdViolations: Long,
        actualViolations: Long
    ): Incident? {
        try {
            // Create incident details
            val incidentDetails = ColumnLevelRuleIncidentDetails(
                timeWindowStart = timeWindowStart,
                timeWindowEnd = timeWindowEnd,
                thresholdRecordsFailing = thresholdViolations,
                actualRecordsFailing = actualViolations,
                fieldName = rule.fieldName
            )

            // Create incident
            val createData = Incident.CreateData(
                tenantId = tenantId,
                ruleId = rule.ruleId,
                incidentDetails = incidentDetails
            )

            val incident = incidentRepository.create(
                tenantId = tenantId,
                dslContext = dslContext,
                createData = createData
            )

            logger.info(
                "Created incident ${incident.incidentId} for rule ${rule.ruleId} (${rule.ruleName}): " +
                    "$actualViolations violations (threshold: $thresholdViolations)"
            )

            return incident
        } catch (e: Exception) {
            logger.error("Error creating incident for rule ${rule.ruleId}", e)
            return null
        }
    }

    private fun sendIncidentNotification(
        incident: Incident,
        timeWindowStart: Instant,
        timeWindowEnd: Instant,
        thresholdViolations: Long,
        actualViolations: Long
    ) {
        try {
            // Get the asset for this rule
            val asset = assetRepository.getByIds(
                tenantId = incident.tenantId,
                dslContext = dslContext,
                ids = listOf(incident.rule.assetId),
                prefetchRules = false
            ).firstOrNull() ?: run {
                logger.error("Asset not found for incident ${incident.incidentId}")
                return
            }

            // Get the data source for this asset
            val dataSource = dataSourceRepository.getByIds(
                tenantId = incident.tenantId,
                dslContext = dslContext,
                ids = listOf(asset.dataSourceId)
            ).firstOrNull() ?: run {
                logger.error("Data source not found for incident ${incident.incidentId}")
                return
            }

            // Get notification targets for this data source
            val notificationTargets = notificationTargetRepository.filter(
                tenantId = incident.tenantId,
                dslContext = dslContext,
                filter = DataSourceIncidentNotificationTargetFilter()
                    .dataSourceIdIn(listOf(dataSource.dataSourceId))
            )

            if (notificationTargets.isEmpty()) {
                logger.info("No notification targets configured for data source ${dataSource.dataSourceId}")
                return
            }

            // Create notification message
            val notificationMessage = IncidentNotificationMessage(
                incidentId = incident.incidentId,
                tenantId = incident.tenantId,
                ruleId = incident.rule.ruleId,
                ruleName = incident.rule.ruleName ?: "",
                assetId = asset.assetId,
                assetName = asset.assetName,
                dataSourceId = dataSource.dataSourceId,
                dataSourceName = dataSource.dataSourceName,
                timeWindowStart = timeWindowStart,
                timeWindowEnd = timeWindowEnd,
                thresholdViolations = thresholdViolations,
                actualViolations = actualViolations,
                fieldName = incident.rule.fieldName,
                createdAt = incident.createdAt,
                notificationTargets = notificationTargets.map { target ->
                    IncidentNotificationMessage.NotificationTarget(
                        notificationType = target.notificationType,
                        targetAddress = target.targetAddress
                    )
                }
            )

            // Send message to RabbitMQ fanout exchange
            rabbitTemplate.convertAndSend(
                "incident.notification.exchange",
                "", // Routing key is ignored for fanout exchanges
                Json.encodeToString(notificationMessage)
            )

            logger.info("Sent incident notification message for incident ${incident.incidentId}")
        } catch (e: Exception) {
            logger.error("Error sending incident notification for incident ${incident.incidentId}", e)
            // Don't re-throw here as we don't want notification failures to affect incident creation
        }
    }
}
