package com.spars.dens.worker.job

import arrow.core.Either
import com.spars.dens.core.datamodel.alert.models.AlertThreshold
import com.spars.dens.core.datamodel.rule.filters.RuleFilter
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.tenant.repository.TenantRepository
import com.spars.dens.core.utils.logging.MonitoredFunction
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toJavaInstant
import kotlinx.datetime.toKotlinInstant
import kotlinx.datetime.toLocalDateTime
import org.jooq.DSLContext
import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.task.TaskExecutor
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.time.temporal.ChronoUnit
import kotlin.time.DurationUnit

@Component
class AlertThresholdJob(
    private val dslContext: DSLContext,
    private val tenantRepository: TenantRepository,
    private val ruleRepository: RuleRepository,
    private val incidentChecker: IncidentChecker,
    @Qualifier("alertThresholdExecutor")
    private val alertThresholdExecutor: TaskExecutor,
    private val logger: Logger
) {
    // Run every hour at minute 0
    @Scheduled(cron = "0 */5 * * * *")
    @MonitoredFunction("Scheduled job that finds rules with alert thresholds and starts checker jobs")
    fun findRulesAndStartCheckerJobs() {
        val now = Clock.System.now().toJavaInstant().truncatedTo(ChronoUnit.MINUTES).toKotlinInstant()

        Either.catch {
            val activeTenants = tenantRepository.fetchAll(dslContext)

            for (tenant in activeTenants) {
                try {
                    // Get all active rules with alert thresholds
                    val activeRules = ruleRepository.filter(
                        tenantId = tenant.tenantId,
                        dslContext = dslContext,
                        filter = RuleFilter()
                            .active()
                            .withAlertThreshold()
                    )

                    // Filter rules that should be checked at this time
                    val rulesToCheck = activeRules.filter { rule ->
                        val threshold = rule.alertThreshold ?: return@filter false
                        shouldCheckAtTime(threshold, now)
                    }

                    // Submit each rule for threshold checking
                    rulesToCheck.forEach { rule ->
                        alertThresholdExecutor.execute {
                            incidentChecker.checkRuleTestsForIncident(tenant.tenantId, rule, now)
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error checking alert thresholds for tenant ${tenant.tenantId}", e)
                }
            }
        }.mapLeft {
            logger.error("Error in alert threshold checker", it)
        }
    }

    private fun shouldCheckAtTime(threshold: AlertThreshold, now: Instant): Boolean {
        val localDateTime = now.toLocalDateTime(TimeZone.UTC)

        return when (threshold.durationUnit) {
            DurationUnit.NANOSECONDS, DurationUnit.MICROSECONDS, DurationUnit.MILLISECONDS, DurationUnit.SECONDS -> {
                // For very small durations, check every 5 minutes
                true
            }
            DurationUnit.MINUTES -> {
                // Check if current minute is a multiple of the duration
                localDateTime.minute % threshold.durationAmount == 0
            }
            DurationUnit.HOURS -> {
                // Check at the start of each N hours
                localDateTime.minute == 0 && localDateTime.hour % threshold.durationAmount == 0
            }
            DurationUnit.DAYS -> {
                // Check once per day at midnight
                localDateTime.hour == 0 && localDateTime.minute == 0
            }
        }
    }
}
