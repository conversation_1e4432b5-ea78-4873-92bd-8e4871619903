package com.spars.dens.worker.config

import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.core.task.TaskExecutor
import org.springframework.core.task.VirtualThreadTaskExecutor
import java.util.concurrent.Semaphore

@Configuration
class ThreadPoolConfig {
    @Bean("folderScanExecutor")
    @Profile("!test")
    fun folderScanExecutor(): TaskExecutor {
        return VirtualThreadTaskExecutor("folder-scan-")
    }

    @Bean("kafkaScanExecutor")
    @Profile("!test")
    fun kafkaScanExecutor(): TaskExecutor {
        return VirtualThreadTaskExecutor("kafka-scan-")
    }

    @Bean("dataQualityRulesExecutor")
    @Profile("!test")
    fun dataQualityRulesExecutor(): TaskExecutor {
        return VirtualThreadTaskExecutor("data-quality-rule-exec-")
    }

    @Bean("alertThresholdExecutor")
    @Profile("!test")
    fun alertThresholdExecutor(): TaskExecutor {
        return VirtualThreadTaskExecutor("alert-threshold-exec-")
    }

    @Bean("streamingJobsConcurrencyLimiter")
    fun streamingJobsConcurrencyLimiter(
        @Value("\${data-quality-job.max-concurrent-streaming-jobs-per-instance}") maxConcurrentStreamingJobsPerInstance: Int
    ): Semaphore {
        return Semaphore(maxConcurrentStreamingJobsPerInstance)
    }
}
