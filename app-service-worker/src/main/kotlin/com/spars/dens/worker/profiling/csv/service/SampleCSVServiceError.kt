package com.spars.dens.worker.profiling.csv.service

sealed class SampleCSVServiceError(
    override val message: String?,
    override val cause: Throwable? = null
) : Error(message, cause) {
    class FileListEmptyError() :
        SampleCSVServiceError(message = "At least one file must be provided!")

    data class HeaderMismatchAcrossFilesError(val fileName: String) :
        SampleCSVServiceError(message = "Header mismatch across files. Filename: $fileName")

    data class InconsistentColumnCountFoundInFileError(val fileName: String, val columnsFound: Int, val columnsExpected: Int) :
        SampleCSVServiceError(message = "Inconsistent column count found in file. Filename: $fileName, Column count found: $columnsFound. Expected: $columnsExpected")

    data class InconsistentColumnCountFoundError(val columnsFound: Int, val columnsExpected: Int) :
        SampleCSVServiceError(message = "Inconsistent column count found. Column count found: $columnsFound. Expected: $columnsExpected")

    data class TooFewLinesError(val linesRead: Int, val linesExpected: Int) :
        SampleCSVServiceError(message = "Too few lines read. Expected at least: $linesExpected, Read: $linesRead")
}
