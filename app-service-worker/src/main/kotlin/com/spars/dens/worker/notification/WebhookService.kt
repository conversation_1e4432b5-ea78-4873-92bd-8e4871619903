package com.spars.dens.worker.notification

import org.slf4j.Logger
import org.springframework.beans.factory.annotation.Value
import org.springframework.retry.annotation.Backoff
import org.springframework.retry.annotation.Retryable
import org.springframework.stereotype.Service
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse
import java.time.Duration

@Service
class WebhookService(
    @Value("\${notification.webhook.enabled:false}") private val webhookEnabled: Boolean,
    private val logger: Logger
) {

    private val httpClient = HttpClient.newBuilder()
        .connectTimeout(Duration.ofSeconds(10))
        .build()

    @Retryable(
        value = [Exception::class],
        maxAttempts = 3,
        backoff = Backoff(delay = 1000, multiplier = 2.0, maxDelay = 10000)
    )
    fun sendWebhook(url: String, payload: String): Boolean {
        if (!webhookEnabled) {
            logger.info("Webhook sending is disabled. Would send webhook to $url")
            return true
        }

        return try {
            val requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(30))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(payload))

            // No authentication headers

            val request = requestBuilder.build()
            val response = httpClient.send(request, HttpResponse.BodyHandlers.ofString())

            when {
                response.statusCode() in 200..299 -> {
                    logger.info("Webhook sent successfully to $url. Status: ${response.statusCode()}")
                    true
                }
                response.statusCode() in 400..499 -> {
                    // 4XX errors are client errors - don't retry
                    logger.error("Webhook failed with client error ${response.statusCode()} for URL $url. Response: ${response.body()}")
                    false
                }
                response.statusCode() in 500..599 -> {
                    // 5XX errors are server errors - retry
                    logger.warn("Webhook failed with server error ${response.statusCode()} for URL $url. Will retry. Response: ${response.body()}")
                    throw RuntimeException("Webhook server error: ${response.statusCode()}")
                }
                else -> {
                    // Unexpected status codes - retry
                    logger.warn("Webhook failed with unexpected status ${response.statusCode()} for URL $url. Will retry. Response: ${response.body()}")
                    throw RuntimeException("Webhook unexpected status: ${response.statusCode()}")
                }
            }
        } catch (e: Exception) {
            logger.error("Error sending webhook to $url", e)
            throw e // Re-throw to trigger retry mechanism
        }
    }
}
