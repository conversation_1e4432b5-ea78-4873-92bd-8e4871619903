package com.spars.dens.worker.folder.service

import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import java.nio.file.Files
import java.nio.file.Path

class FolderServiceTest {

    private val testRootDataFolder = "/tmp/dens/data"
    private val testTenantId = TenantFixture().tenantId

    @BeforeEach
    fun setUp() {
        Files.createDirectories(Path.of(testRootDataFolder))
    }

    @AfterEach
    fun tearDown() {
        // Walk through the file tree from the root
        Files.walk(Path.of(testRootDataFolder))
            // Sort in reverse order so children get deleted before parents
            .sorted(Comparator.reverseOrder())
            // For each file/directory, call Files.delete
            .forEach { Files.delete(it) }
    }
}
