package com.spars.dens.worker.dataquality.kafka

import io.confluent.kafka.schemaregistry.client.CachedSchemaRegistryClient
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import org.slf4j.Logger
import org.slf4j.LoggerFactory

/**
 * Helper class for managing schema registry operations in tests.
 * Handles schema registration and cleanup for AVRO and PROTOBUF schemas.
 */
class TestSchemaRegistryHelper(
    private val schemaRegistryUrl: String
) {
    private val logger: Logger = LoggerFactory.getLogger(TestSchemaRegistryHelper::class.java)
    private val schemaRegistryClient: SchemaRegistryClient = CachedSchemaRegistryClient(schemaRegistryUrl, 100)

    /**
     * Registers an AVRO schema in the schema registry.
     */
    fun registerAvroSchema(subject: String, schemaString: String): Int {
        return try {
            val schema = org.apache.avro.Schema.Parser().parse(schemaString)
            val avroSchema = io.confluent.kafka.schemaregistry.avro.AvroSchema(schema)
            val schemaId = schemaRegistryClient.register(subject, avroSchema)
            logger.info("Registered AVRO schema for subject '$subject' with ID: $schemaId")
            schemaId
        } catch (e: Exception) {
            logger.error("Failed to register AVRO schema for subject '$subject'", e)
            throw e
        }
    }

    /**
     * Registers a PROTOBUF schema in the schema registry.
     */
    fun registerProtobufSchema(subject: String, schemaString: String): Int {
        return try {
            val protobufSchema = io.confluent.kafka.schemaregistry.protobuf.ProtobufSchema(schemaString)
            val schemaId = schemaRegistryClient.register(subject, protobufSchema)
            logger.info("Registered PROTOBUF schema for subject '$subject' with ID: $schemaId")
            schemaId
        } catch (e: Exception) {
            logger.error("Failed to register PROTOBUF schema for subject '$subject'", e)
            throw e
        }
    }

    /**
     * Registers a JSON schema in the schema registry.
     */
    fun registerJsonSchema(subject: String, schemaString: String): Int {
        return try {
            val jsonSchema = io.confluent.kafka.schemaregistry.json.JsonSchema(schemaString)
            val schemaId = schemaRegistryClient.register(subject, jsonSchema)
            logger.info("Registered JSON schema for subject '$subject' with ID: $schemaId")
            schemaId
        } catch (e: Exception) {
            logger.error("Failed to register JSON schema for subject '$subject'", e)
            throw e
        }
    }

    /**
     * Deletes a subject and all its versions from the schema registry.
     */
    fun deleteSubject(subject: String) {
        try {
            val versions = schemaRegistryClient.deleteSubject(subject)
            logger.info("Deleted subject '$subject' with versions: $versions")
        } catch (e: Exception) {
            logger.warn("Failed to delete subject '$subject': ${e.message}")
        }
    }

    /**
     * Gets all subjects from the schema registry.
     */
    fun getAllSubjects(): List<String> {
        return try {
            schemaRegistryClient.allSubjects.toList()
        } catch (e: Exception) {
            logger.error("Failed to get all subjects", e)
            emptyList()
        }
    }

    /**
     * Checks if the schema registry is healthy.
     */
    fun isHealthy(): Boolean {
        return try {
            schemaRegistryClient.allSubjects
            true
        } catch (e: Exception) {
            logger.error("Schema registry health check failed", e)
            false
        }
    }

    /**
     * Cleans up test subjects by deleting them.
     */
    fun cleanupTestSubjects(testSubjects: List<String>) {
        testSubjects.forEach { subject ->
            deleteSubject(subject)
        }
    }

    fun close() {
        try {
            // Schema registry client doesn't have a close method, but we can clear cache
            logger.info("Schema registry helper closed")
        } catch (e: Exception) {
            logger.warn("Error closing schema registry helper", e)
        }
    }
}
