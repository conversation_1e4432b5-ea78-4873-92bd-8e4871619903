package com.spars.dens.worker.profiling.json.service

import arrow.core.getOrElse
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.spars.dens.core.datamodel.asset.services.TypeMapperService
import com.spars.dens.worker.profiling.common.services.inference.rule.EnumRuleChecker
import com.spars.dens.worker.profiling.common.services.inference.type.DateTimeTypeResolver
import com.spars.dens.worker.profiling.common.services.inference.type.DecimalNumberTypeResolver
import com.spars.dens.worker.profiling.common.services.inference.type.TimeTypeResolver
import com.spars.dens.worker.profiling.common.services.inference.type.WholeNumberTypeResolver
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.slf4j.Logger

@ExtendWith(MockitoExtension::class)
class ResolveJSONSchemaServiceTest {

    @Mock
    private lateinit var logger: Logger

    private lateinit var objectMapper: ObjectMapper
    private lateinit var resolveJSONSchemaService: ResolveJSONSchemaService

    @BeforeEach
    fun setUp() {
        objectMapper = jacksonObjectMapper()
        resolveJSONSchemaService = ResolveJSONSchemaService(objectMapper, logger, listOf(DateTimeTypeResolver(), DateTimeTypeResolver(), TimeTypeResolver(), WholeNumberTypeResolver(), DecimalNumberTypeResolver()), listOf(), EnumRuleChecker(), TypeMapperService())
    }

    @Test
    fun `test resolveSchema with simple JSON objects`() {
        // Given
        val jsonStr1 = """{"id": 1, "name": "John", "age": 30, "isActive": true}"""
        val jsonStr2 = """{"id": 2, "name": "Jane", "age": 25, "isActive": false}"""
        val jsonStrings = listOf(jsonStr1, jsonStr2)

        // When
        val result = resolveJSONSchemaService.resolveSchema(jsonStrings)

        // Then
        assertTrue(result.isRight())
        val schema = result.getOrElse { throw it }.schema

        // Verify schema structure
        assertTrue(schema.has("type"))
        assertEquals("object", schema.get("type").asText())
        assertTrue(schema.has("properties"))

        // Check properties
        val properties = schema.get("properties")
        assertTrue(properties.has("id"))
        assertTrue(properties.has("name"))
        assertTrue(properties.has("age"))
        assertTrue(properties.has("isActive"))

        // Check property types
        assertEquals("integer", properties.get("id").get("type").asText())
        assertEquals("string", properties.get("name").get("type").asText())
        assertEquals("integer", properties.get("age").get("type").asText())
        assertEquals("boolean", properties.get("isActive").get("type").asText())
    }

    @Test
    fun `test resolveSchema with nested objects`() {
        // Given
        val jsonStr1 = """{"id": 1, "user": {"name": "John", "email": "<EMAIL>"}}"""
        val jsonStr2 = """{"id": 2, "user": {"name": "Jane", "email": "<EMAIL>"}}"""
        val jsonStrings = listOf(jsonStr1, jsonStr2)

        // When
        val result = resolveJSONSchemaService.resolveSchema(jsonStrings)

        // Then
        assertTrue(result.isRight())
        val schema = result.getOrElse { throw it }.schema

        // Verify schema structure
        assertTrue(schema.has("type"))
        assertEquals("object", schema.get("type").asText())
        assertTrue(schema.has("properties"))

        // Check properties
        val properties = schema.get("properties")
        assertTrue(properties.has("id"))
        assertTrue(properties.has("user"))

        // Check nested object
        val userProperties = properties.get("user").get("properties")
        assertTrue(userProperties.has("name"))
        assertTrue(userProperties.has("email"))
        assertEquals("string", userProperties.get("name").get("type").asText())
        assertEquals("string", userProperties.get("email").get("type").asText())
    }

    @Test
    fun `test resolveSchema with arrays`() {
        // Given
        val jsonStr1 = """{"id": 1, "tags": ["java", "kotlin"]}"""
        val jsonStr2 = """{"id": 2, "tags": ["python", "javascript"]}"""
        val jsonStrings = listOf(jsonStr1, jsonStr2)

        // When
        val result = resolveJSONSchemaService.resolveSchema(jsonStrings)

        // Then
        assertTrue(result.isRight())
        val schema = result.getOrElse { throw it }.schema

        // Verify schema structure
        val properties = schema.get("properties")
        assertTrue(properties.has("tags"))

        // Check array type
        val tagsProperty = properties.get("tags")
        assertEquals("array", tagsProperty.get("type").asText())

        // Check array items
        assertTrue(tagsProperty.has("items"))
        assertEquals("string", tagsProperty.get("items").get("type").asText())
    }

    @Test
    fun `test resolveSchema with empty input`() {
        // Given
        val jsonStrings = emptyList<String>()

        // When
        val result = resolveJSONSchemaService.resolveSchema(jsonStrings)

        // Then
        assertTrue(result.isLeft())
        val error = result.swap().getOrElse { throw IllegalStateException("Expected Left but got Right") }
        assertTrue(error is ResolveJSONSchemaServiceError.EmptyInputError)
    }

    @Test
    fun `test resolveSchema with invalid JSON`() {
        // Given
        val jsonStrings = listOf(
            """{"id": 1, "name": "John"}""",
            """invalid json""",
            """{"id": 2, "name": "Jane"}"""
        )

        // When
        val result = resolveJSONSchemaService.resolveSchema(jsonStrings)

        // Then
        assertTrue(result.isRight(), "Expected Right but got Left: ${result.swap().getOrNull()}")
        val schema = result.getOrElse { throw it }.schema

        // Verify schema structure
        val properties = schema.get("properties")
        assertTrue(properties.has("id"))
        assertTrue(properties.has("name"))
    }

    @Test
    fun `test inferCommonStructure with simple objects`() {
        // Given
        val jsonStr1 = """{"id": 1, "name": "John", "age": 30}"""
        val jsonStr2 = """{"id": 2, "name": "Jane", "email": "<EMAIL>"}"""
        val jsonStrings = listOf(jsonStr1, jsonStr2)

        // When
        val result = resolveJSONSchemaService.inferCommonStructure(jsonStrings, 0.5)

        // Then
        assertTrue(result.has("id"))
        assertTrue(result.has("name"))
        // age and email are only in one document each, but with threshold 0.5 they should be included
        assertTrue(result.has("age"))
        assertTrue(result.has("email"))
    }

    @Test
    fun `test inferCommonStructure with higher threshold`() {
        // Given
        val jsonStr1 = """{"id": 1, "name": "John", "age": 30}"""
        val jsonStr2 = """{"id": 2, "name": "Jane", "email": "<EMAIL>"}"""
        val jsonStrings = listOf(jsonStr1, jsonStr2)

        // When
        val result = resolveJSONSchemaService.inferCommonStructure(jsonStrings, 1.0)

        // Then
        assertTrue(result.has("id"))
        assertTrue(result.has("name"))
        // With threshold 1.0, only fields in all documents should be included
        assertEquals(false, result.has("age"))
        assertEquals(false, result.has("email"))
    }
}
