package com.spars.dens.worker.dataquality.kafka

import com.google.protobuf.DescriptorProtos
import com.google.protobuf.Descriptors
import com.google.protobuf.DynamicMessage
import io.confluent.kafka.serializers.protobuf.KafkaProtobufSerializer
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*

/**
 * Helper class for producing PROTOBUF messages to Kafka topics in tests.
 * Uses Confluent's PROTOBUF serializer with schema registry integration.
 */
class ProtobufTestMessageProducer(
    private val bootstrapServers: String,
    private val schemaRegistryUrl: String
) {
    private val logger: Logger = LoggerFactory.getLogger(ProtobufTestMessageProducer::class.java)
    private val producer: KafkaProducer<String, DynamicMessage>

    init {
        val props = Properties().apply {
            put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers)
            put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer::class.java.name)
            put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaProtobufSerializer::class.java.name)
            put("schema.registry.url", schemaRegistryUrl)
            put(ProducerConfig.ACKS_CONFIG, "all")
            put(ProducerConfig.RETRIES_CONFIG, 3)
            put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true)
        }
        producer = KafkaProducer(props)
    }

    /**
     * Creates a PROTOBUF descriptor for OrderEvent that matches the test schema.
     * This simulates how a real data quality system would work with dynamic schemas.
     */
    fun createOrderEventDescriptor(): Descriptors.Descriptor {
        // Create simple OrderEvent message matching the test schema exactly
        val orderEventMessage = DescriptorProtos.DescriptorProto.newBuilder()
            .setName("OrderEvent")
            .addField(
                DescriptorProtos.FieldDescriptorProto.newBuilder()
                    .setName("orderId").setNumber(1).setType(DescriptorProtos.FieldDescriptorProto.Type.TYPE_STRING)
            )
            .addField(
                DescriptorProtos.FieldDescriptorProto.newBuilder()
                    .setName("customerId").setNumber(2).setType(DescriptorProtos.FieldDescriptorProto.Type.TYPE_STRING)
            )
            .addField(
                DescriptorProtos.FieldDescriptorProto.newBuilder()
                    .setName("amount").setNumber(3).setType(DescriptorProtos.FieldDescriptorProto.Type.TYPE_DOUBLE)
            )
            .addField(
                DescriptorProtos.FieldDescriptorProto.newBuilder()
                    .setName("status").setNumber(4).setType(DescriptorProtos.FieldDescriptorProto.Type.TYPE_STRING)
            )
            .addField(
                DescriptorProtos.FieldDescriptorProto.newBuilder()
                    .setName("timestamp").setNumber(5).setType(DescriptorProtos.FieldDescriptorProto.Type.TYPE_INT64)
            )
            .build()

        // Create file descriptor
        val fileDescriptor = DescriptorProtos.FileDescriptorProto.newBuilder()
            .setName("order_event.proto")
            .setPackage("com.datapakt.orders")
            .addMessageType(orderEventMessage)
            .build()

        val fileDescriptorBuilt = Descriptors.FileDescriptor.buildFrom(fileDescriptor, emptyArray())
        return fileDescriptorBuilt.findMessageTypeByName("OrderEvent")
            ?: throw IllegalStateException("OrderEvent message type not found")
    }

    /**
     * Produces PROTOBUF order event messages to the specified topic.
     */
    fun produceOrderEventMessages(topicName: String) {
        val messageDescriptor = createOrderEventDescriptor()
        val messages = createOrderEventMessages(messageDescriptor)

        messages.forEachIndexed { index, message ->
            val producerRecord = ProducerRecord(topicName, "order-key-$index", message)
            try {
                val metadata = producer.send(producerRecord).get()
                logger.info("Produced PROTOBUF message to topic: ${metadata.topic()}, partition: ${metadata.partition()}, offset: ${metadata.offset()}")
            } catch (e: Exception) {
                logger.error("Failed to produce PROTOBUF message", e)
                throw e
            }
        }
        producer.flush()
    }

    private fun createOrderEventMessages(messageDescriptor: Descriptors.Descriptor): List<DynamicMessage> {
        return listOf(
            // Valid messages (should pass all rules)
            // orderId not null AND amount between 0.01 and 10000.0
            createOrderEvent(messageDescriptor, "ORD001", "CUST001", 99.99, "PENDING"),
            createOrderEvent(messageDescriptor, "ORD002", "CUST002", 149.50, "COMPLETED"),

            // Invalid messages (should fail some rules)
            createOrderEvent(messageDescriptor, null, "CUST003", 75.00, "PENDING"), // orderId null (fails rule 1)
            createOrderEvent(messageDescriptor, "ORD003", "CUST004", -25.00, "FAILED") // amount < 0.01 (fails rule 2)
        )
    }

    private fun createOrderEvent(
        messageDescriptor: Descriptors.Descriptor,
        orderId: String?,
        customerId: String,
        amount: Double,
        status: String
    ): DynamicMessage {
        val builder = DynamicMessage.newBuilder(messageDescriptor)

        // Set fields matching the simple test schema
        // For PROTOBUF, we need to set all fields, using empty string for null values
        builder.setField(messageDescriptor.findFieldByName("orderId"), orderId ?: "")
        builder.setField(messageDescriptor.findFieldByName("customerId"), customerId)
        builder.setField(messageDescriptor.findFieldByName("amount"), amount)
        builder.setField(messageDescriptor.findFieldByName("status"), status)
        builder.setField(messageDescriptor.findFieldByName("timestamp"), System.currentTimeMillis())

        logger.info("Created PROTOBUF message: orderId=${orderId ?: ""}, customerId=$customerId, amount=$amount, status=$status")

        return builder.build()
    }

    fun close() {
        producer.close()
    }
}
