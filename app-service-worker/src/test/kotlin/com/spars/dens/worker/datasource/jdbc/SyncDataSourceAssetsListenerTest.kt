package com.spars.dens.worker.datasource.jdbc

import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.asset.repositories.AssetSchemaVersionRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.models.SyncDataSourceAssetsMessage
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import org.jooq.DSLContext
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.verifyNoInteractions
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension
import org.slf4j.Logger

@Disabled
@ExtendWith(MockitoExtension::class)
class SyncDataSourceAssetsListenerTest {

    @Mock
    private lateinit var dataSourceRepository: DataSourceRepository

    @Mock
    private lateinit var assetRepository: AssetRepository

    @Mock
    private lateinit var assetSchemaVersionRepository: AssetSchemaVersionRepository

    @Mock
    private lateinit var dslContext: DSLContext

    @Mock
    private lateinit var logger: Logger

    private lateinit var listener: SyncDataSourceAssetsListener

    @BeforeEach
    fun setUp() {
        listener = SyncDataSourceAssetsListener(
            dataSourceRepository = dataSourceRepository,
            assetRepository = assetRepository,
            assetSchemaVersionRepository = assetSchemaVersionRepository,
            dslContext = dslContext,
            logger = logger
        )
    }

    @Test
    fun `test processMessage with non-JDBC data source`() {
        // Create a non-JDBC data source
        val dataSourceFixture = DataSourceFixture(
            dataSourceType = DataSourceType.KAFKA
        )

        // Create a sync message
        val syncMessage = SyncDataSourceAssetsMessage(
            tenantId = dataSourceFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId
        )

        // Mock the data source repository
        `when`(
            dataSourceRepository.getByIds(
                tenantId = dataSourceFixture.tenantId,
                ids = listOf(dataSourceFixture.dataSourceId),
                dslContext = dslContext
            )
        ).thenReturn(listOf(dataSourceFixture.dataSource))

        // Process the message
        listener.processMessage(Json.encodeToString(syncMessage))

        // Verify that a warning was logged
        verify(logger).warn("Data source is not a JDBC type: ${dataSourceFixture.dataSourceType}")

        // Verify that no other interactions occurred
        verifyNoInteractions(assetRepository)
        verifyNoInteractions(assetSchemaVersionRepository)
    }

    @Test
    fun `test processMessage with data source not found`() {
        // Create a sync message
        val syncMessage = SyncDataSourceAssetsMessage(
            tenantId = DataSourceFixture().tenantId,
            dataSourceId = DataSourceFixture().dataSourceId
        )

        // Mock the data source repository to return null
        `when`(
            dataSourceRepository.getByIds(
                tenantId = syncMessage.tenantId,
                ids = listOf(syncMessage.dataSourceId),
                dslContext = dslContext
            )
        ).thenReturn(null)

        // Process the message
        listener.processMessage(Json.encodeToString(syncMessage))

        // Verify that an error was logged
        verify(logger).error("Data source not found: ${syncMessage.dataSourceId}")

        // Verify that no other interactions occurred
        verifyNoInteractions(assetRepository)
        verifyNoInteractions(assetSchemaVersionRepository)
    }
}
