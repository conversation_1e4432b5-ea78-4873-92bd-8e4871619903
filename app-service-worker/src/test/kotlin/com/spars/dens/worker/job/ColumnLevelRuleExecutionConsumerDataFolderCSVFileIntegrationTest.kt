package com.spars.dens.worker.job

import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.fixtures.AssetSchemaVersionFixture
import com.spars.dens.core.datamodel.asset.models.AssetType
import com.spars.dens.core.datamodel.asset.models.AssetTypeSpecificSettings
import com.spars.dens.core.datamodel.asset.models.Column
import com.spars.dens.core.datamodel.asset.models.FieldType
import com.spars.dens.core.datamodel.asset.models.Schema
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.dataformat.models.DataFormat
import com.spars.dens.core.datamodel.dataformat.models.DataFormatSpecificSettings
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcess
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcessId
import com.spars.dens.core.datamodel.file.repositories.AssetDataChunkInProcessRepository
import com.spars.dens.core.datamodel.folder.models.FileNameFilter
import com.spars.dens.core.datamodel.folder.models.FileSystemProtocol
import com.spars.dens.core.datamodel.process.ProcessStatus
import com.spars.dens.core.datamodel.rule.filters.RuleSetExecutionFilter
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.ColumnLevelRulesMessage
import com.spars.dens.core.datamodel.rule.models.FileChunkDetails
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.models.ValidationRule
import com.spars.dens.core.datamodel.rule.models.comparable.NumberValue
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.rule.repositories.RuleSetExecutionRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import com.spars.dens.datamodel.jooq.tables.references.ASSET
import com.spars.dens.datamodel.jooq.tables.references.ASSET_DATA_CHUNK_IN_PROCESS
import com.spars.dens.datamodel.jooq.tables.references.ASSET_SCHEMA_VERSION
import com.spars.dens.datamodel.jooq.tables.references.DATA_SOURCE
import com.spars.dens.datamodel.jooq.tables.references.RULE
import com.spars.dens.datamodel.jooq.tables.references.RULESET_EXECUTION
import com.spars.dens.datamodel.jooq.tables.references.TENANT
import com.spars.dens.worker.BaseWorkerIntegrationTest
import kotlinx.datetime.Clock
import kotlinx.serialization.json.Json
import org.apache.commons.vfs2.FileSystemOptions
import org.apache.commons.vfs2.Selectors
import org.apache.commons.vfs2.auth.StaticUserAuthenticator
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder
import org.apache.commons.vfs2.impl.StandardFileSystemManager
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.transaction.TestTransaction
import java.io.ByteArrayInputStream
import java.util.concurrent.TimeUnit

class ColumnLevelRuleExecutionConsumerDataFolderCSVFileIntegrationTest : BaseWorkerIntegrationTest() {

    @Autowired
    private lateinit var assetRepository: AssetRepository

    @Autowired
    private lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    private lateinit var ruleRepository: RuleRepository

    @Autowired
    private lateinit var assetDataChunkInProcessRepository: AssetDataChunkInProcessRepository

    @Autowired
    private lateinit var ruleSetExecutionRepository: RuleSetExecutionRepository

    @Autowired
    private lateinit var rabbitTemplate: RabbitTemplate

    private val sftpDataSourceConnectionSettings = ConnectionSettings.FolderConnectionSettings(
        protocol = FileSystemProtocol.SFTP,
        host = "localhost",
        port = 2222,
        userName = "user",
        password = "password"
    )

    private val dataSourceFixture = DataSourceFixture(
        dataSourceType = DataSourceType.FOLDER,
        connectionSettings = sftpDataSourceConnectionSettings
    )

    private val testFolderName = "/data/unit_test_folder"
    private val testFileName = "test_file.csv"
    private val dataChunkId = AssetDataChunkInProcessId.from(3412991387939897715)
    private val ruleId1 = RuleId.from(2712329368754886200L)
    private val ruleId2 = RuleId.from(416520184140944958L)

    private val assetFixture = AssetFixture(
        assetType = AssetType.DATA_FOLDER,
        dataSourceId = dataSourceFixture.dataSourceId,
        currentSchemaFixture = AssetSchemaVersionFixture(
            schema = Schema.CsvSchema(
                columns = listOf(
                    Column(
                        name = "id",
                        type = FieldType.NUMBER,
                        required = null,
                        typeInSource = null,
                        format = null,
                        constraints = null
                    ),
                    Column(
                        name = "name",
                        type = FieldType.TEXT,
                        required = null,
                        typeInSource = null,
                        format = null,
                        constraints = null
                    ),
                    Column(
                        name = "age",
                        type = FieldType.NUMBER,
                        required = null,
                        typeInSource = null,
                        format = null,
                        constraints = null
                    )
                )
            ),
            dataFormat = DataFormat.CSV,
            dataFormatSpecificSettings = DataFormatSpecificSettings.CsvDataFormatSettings(
                delimiter = ",",
                header = false,
                quote = "\"",
                escape = "\\"
            )
        ),
        assetTypeSpecificSettings = AssetTypeSpecificSettings.DataFolderAssetSettings(
            baseFolder = testFolderName,
            baseInstant = null,
            fileNameFilter = FileNameFilter.RegexFilter(
                pattern = ".*\\.csv"
            ),
            compressed = false
        )
    )
    private lateinit var fsManager: StandardFileSystemManager

    @BeforeEach
    override fun setup() {
        super.setup()
        // Setup VFS for SFTP
        val authenticator = StaticUserAuthenticator(null, sftpDataSourceConnectionSettings.userName, sftpDataSourceConnectionSettings.password)
        val fsOptions = FileSystemOptions()
        // Set authenticator
        DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(fsOptions, authenticator)

        // Configure FTP settings
        FtpFileSystemConfigBuilder.getInstance().setPassiveMode(fsOptions, true)

        // Configure SFTP settings
        SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(fsOptions, "no")

        // Initialize file system manager
        fsManager = StandardFileSystemManager()
        fsManager.init()
        // Construct URI from components
        val baseUri = sftpDataSourceConnectionSettings.getBaseFolderURIString()
        val cleanPath = testFolderName.removePrefix("/")
        val folderURI = "$baseUri/$cleanPath"

        // Create test folder on SFTP server

        val testFolder = fsManager.resolveFile(folderURI, fsOptions)
        testFolder.createFolder()

        // Create test CSV file with some violations
        val testFileObject = testFolder.resolveFile(testFileName)
        val testFilePath = testFileObject.name.path

        val csvContent = """
            1,John,25
            2,,30
            3,Bob,-5
            4,Alice,150
        """.trimIndent()

        testFileObject.content.outputStream.use { out ->
            ByteArrayInputStream(csvContent.toByteArray()).copyTo(out)
        }
        dataSourceRepository.create(
            tenantId = dataSourceFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )

        // Create asset with CSV schema
        assetRepository.create(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            createData = assetFixture.createData
        )

        // Create rules
        // 1. Name not null rule
        val nameNotNullRule = FieldRuleFixture(
            ruleId = ruleId1,
            assetId = assetFixture.assetId,
            fieldName = "name",
            ruleName = "Name must not be null",
            validationRule = ValidationRule.FieldNotNullValidationRule()
        )

        // 2. Age range rule (0-120)
        val ageRangeRule = FieldRuleFixture(
            ruleId = ruleId2,
            assetId = assetFixture.assetId,
            fieldName = "age",
            ruleName = "Age must be between 0 and 120",
            validationRule = ValidationRule.FieldRangeValidationRule(
                minValue = NumberValue(0),
                maxValue = NumberValue(120),
                minInclusive = true,
                maxInclusive = true
            )
        )

        ruleRepository.create(
            tenantId = nameNotNullRule.tenantId,
            dslContext = dslContext,
            dataItems = listOf(nameNotNullRule.createData, ageRangeRule.createData)
        )

        // Create file log entry
        val fileAssetDataChunkCreateData = AssetDataChunkInProcess.CreateData(
            tenantId = assetFixture.tenantId,
            assetDataChunkInProcessId = dataChunkId,
            assetId = assetFixture.assetId,
            dataChunkDetails = FileChunkDetails(
                filePath = testFilePath,
                fileSize = 300,
            ),
            processedStatus = ProcessStatus.QUEUED,
            createdAt = Clock.System.now(),
            numItems = null,
            processedStatusDetails = null,
            numTrials = 0
        )
        assetDataChunkInProcessRepository.create(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(fileAssetDataChunkCreateData)
        )
        TestTransaction.flagForCommit()
        TestTransaction.end()
    }

    @AfterEach
    fun cleanup() {
        // Clean up test files on SFTP
        try {
            dslContext.deleteFrom(RULESET_EXECUTION)
                .where(RULESET_EXECUTION.ASSET_ID.eq(assetFixture.assetId.value))
                .execute()
            dslContext.deleteFrom(ASSET_DATA_CHUNK_IN_PROCESS)
                .where(ASSET_DATA_CHUNK_IN_PROCESS.ASSET_ID.eq(assetFixture.assetId.value))
                .execute()
            dslContext
                .deleteFrom(ASSET_SCHEMA_VERSION)
                .where(ASSET_SCHEMA_VERSION.ASSET_ID.eq(assetFixture.assetId.value))
                .execute()

            dslContext.deleteFrom(RULE)
                .where(RULE.ASSET_ID.eq(assetFixture.assetId.value))
                .execute()
            // 5) delete the asset itself
            dslContext
                .deleteFrom(ASSET)
                .where(ASSET.ASSET_ID.eq(assetFixture.assetId.value))
                .execute()

            // 6) finally delete the data‐source
            dslContext
                .deleteFrom(DATA_SOURCE)
                .where(DATA_SOURCE.DATA_SOURCE_ID.eq(dataSourceFixture.dataSourceId.value))
                .execute()
            dslContext.deleteFrom(TENANT)
                .where(TENANT.TENANT_ID.eq(assetFixture.tenantId.value))
                .execute()
            TestTransaction.flagForCommit()
            TestTransaction.end()

            val authenticator = StaticUserAuthenticator(null, sftpDataSourceConnectionSettings.userName, sftpDataSourceConnectionSettings.password)
            val fsOptions = FileSystemOptions()
            DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(fsOptions, authenticator)
            SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(fsOptions, "no")

            val baseUri = sftpDataSourceConnectionSettings.getBaseFolderURIString()
            val cleanPath = testFolderName.removePrefix("/")
            val folderURI = "$baseUri/$cleanPath"
            val testFolder = fsManager.resolveFile(folderURI, fsOptions)
            if (testFolder.exists()) {
                testFolder.delete(Selectors.SELECT_ALL)
            }
        } catch (e: Exception) {
            println("Error cleaning up SFTP files: ${e.message}")
        } finally {
            fsManager.close()
        }
    }

    @Test
    fun `test column level rule execution consumer processes message correctly`() {
        TestTransaction.start()
        // Create message
        val message = ColumnLevelRulesMessage(
            tenantId = TenantFixture().tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(ruleId1, ruleId2),
            plannedStartTime = Clock.System.now(),
            dataChunkId = dataChunkId
        )

        // Send message to RabbitMQ
        rabbitTemplate.convertAndSend("rule-execution.column-level", Json.encodeToString(ColumnLevelRulesMessage.serializer(), message))

        // Wait for processing
        TimeUnit.SECONDS.sleep(5)

        // Verify file log status is updated to COMPLETED
        val updatedFileLogs = assetDataChunkInProcessRepository.getByIds(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(dataChunkId),
            ignoreDeleted = false
        )
        assertThat(updatedFileLogs).hasSize(1)
        assertThat(updatedFileLogs[0].processedStatus).isEqualTo(ProcessStatus.COMPLETED)

        // Verify rule set execution is created
        val ruleSetExecutions = ruleSetExecutionRepository.filter(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            filter = RuleSetExecutionFilter().assetIdIn(listOf(assetFixture.assetId))
        )
        assertThat(ruleSetExecutions).hasSize(1)
        val execution = ruleSetExecutions[0]
        assertThat(execution.ruleIds).containsExactlyInAnyOrder(ruleId1, ruleId2)
        assertThat(execution.numRowsScanned).isEqualTo(4) // Total rows in CSV
        assertThat(execution.numRowsPassingAllRules).isEqualTo(1) // Some rows should pass
        assertThat(execution.numRowsFailingAnyRule).isEqualTo(3) // Some rows should fail
        assertThat(execution.overallDataQualityScore).isEqualTo(25.0) // 1 passing out of 4 total
    }
}
