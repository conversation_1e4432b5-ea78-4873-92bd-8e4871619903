package com.spars.dens.worker.notification

import com.spars.dens.core.datamodel.asset.models.AssetId
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.incident.models.IncidentId
import com.spars.dens.core.datamodel.incident.models.IncidentNotificationMessage
import com.spars.dens.core.datamodel.notification.models.DataSourceIncidentNotificationTarget
import com.spars.dens.core.datamodel.notification.models.NotificationType
import com.spars.dens.core.datamodel.notification.models.WebhookCredentials
import com.spars.dens.core.datamodel.notification.repositories.DataSourceIncidentNotificationTargetRepository
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import com.spars.dens.worker.BaseWorkerIntegrationTest
import com.sun.net.httpserver.HttpHandler
import com.sun.net.httpserver.HttpServer
import kotlinx.serialization.json.Json
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource
import java.io.BufferedReader
import java.io.InputStreamReader
import java.net.InetSocketAddress
import java.util.Base64
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.Executors

@TestPropertySource(properties = ["notification.webhook.enabled=true"])
class WebhookNotificationIntegrationTest : BaseWorkerIntegrationTest() {

    @Autowired
    private lateinit var webhookNotificationConsumer: WebhookNotificationConsumer

    @Autowired
    private lateinit var webhookService: WebhookService

    @Autowired
    private lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    private lateinit var dataSourceIncidentNotificationTargetRepository: DataSourceIncidentNotificationTargetRepository

    private lateinit var httpServer: HttpServer
    private val receivedRequests = ConcurrentLinkedQueue<ReceivedRequest>()
    private val tenantFixture = TenantFixture()
    private val dataSourceFixture = DataSourceFixture()

    data class ReceivedRequest(
        val method: String,
        val path: String,
        val headers: Map<String, String>,
        val body: String
    )

    @BeforeEach
    fun setupWebhookTest() {
        // Create test HTTP server
        httpServer = HttpServer.create(InetSocketAddress(8089), 0)
        httpServer.executor = Executors.newFixedThreadPool(1)

        // Add handlers for different endpoints
        val handler = HttpHandler { exchange ->
            val requestBody = BufferedReader(InputStreamReader(exchange.requestBody)).readText()
            // Convert headers to lowercase for case-insensitive lookup
            val headers = exchange.requestHeaders.mapKeys { it.key.lowercase() }
                .mapValues { it.value.firstOrNull() ?: "" }

            receivedRequests.add(
                ReceivedRequest(
                    method = exchange.requestMethod,
                    path = exchange.requestURI.path,
                    headers = headers,
                    body = requestBody
                )
            )

            // Send appropriate response based on path
            when (exchange.requestURI.path) {
                "/webhook-error" -> {
                    exchange.sendResponseHeaders(500, 0)
                }
                "/webhook-client-error" -> {
                    exchange.sendResponseHeaders(400, 0)
                }
                else -> {
                    exchange.sendResponseHeaders(200, 0)
                }
            }
            exchange.responseBody.close()
        }

        httpServer.createContext("/webhook", handler)
        httpServer.createContext("/webhook-error", handler)
        httpServer.createContext("/webhook-client-error", handler)
        httpServer.createContext("/webhook1", handler)
        httpServer.createContext("/webhook2", handler)

        httpServer.start()

        // Create test data source
        dataSourceRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )

        // Clear received requests
        receivedRequests.clear()
    }

    @AfterEach
    fun tearDown() {
        httpServer.stop(0)
    }

    @Test
    fun `should send webhook notification without credentials`() {
        // Create webhook notification target without credentials
        val webhookTarget = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = tenantFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "http://localhost:8089/webhook",
            credentials = null
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(webhookTarget)
        )

        // Create incident notification message
        val incidentMessage = IncidentNotificationMessage(
            incidentId = IncidentId.from(1L),
            tenantId = tenantFixture.tenantId,
            ruleId = RuleId.from(1L),
            ruleName = "Test Rule",
            assetId = AssetId.from(1L),
            assetName = "Test Asset",
            dataSourceId = dataSourceFixture.dataSourceId,
            dataSourceName = "Test Data Source",
            timeWindowStart = kotlinx.datetime.Clock.System.now(),
            timeWindowEnd = kotlinx.datetime.Clock.System.now(),
            thresholdViolations = 10L,
            actualViolations = 5L,
            fieldName = null,
            createdAt = kotlinx.datetime.Clock.System.now(),
            notificationTargets = emptyList()
        )

        val messageJson = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

        // Process the notification
        webhookNotificationConsumer.handleIncidentNotification(messageJson)

        // Wait a bit for async processing
        Thread.sleep(100)

        // Verify webhook was called
        assertThat(receivedRequests).hasSize(1)
        val request = receivedRequests.first()
        assertThat(request.method).isEqualTo("POST")
        assertThat(request.path).isEqualTo("/webhook")
        assertThat(request.headers["content-type"]).isEqualTo("application/json")
        assertThat(request.headers).doesNotContainKey("authorization")
        assertThat(request.body).contains("Test Rule")
    }

    @Test
    fun `should send webhook notification with basic authentication`() {
        // Create webhook notification target with basic auth credentials
        val basicAuthCredentials = WebhookCredentials.BasicAuthenticationCredentials(
            username = "testuser",
            password = "testpass"
        )

        val webhookTarget = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = tenantFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "http://localhost:8089/webhook",
            credentials = basicAuthCredentials
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(webhookTarget)
        )

        // Create incident notification message
        val incidentMessage = IncidentNotificationMessage(
            incidentId = IncidentId.from(2L),
            tenantId = tenantFixture.tenantId,
            ruleId = RuleId.from(2L),
            ruleName = "Test Rule with Auth",
            assetId = AssetId.from(2L),
            assetName = "Test Asset with Auth",
            dataSourceId = dataSourceFixture.dataSourceId,
            dataSourceName = "Test Data Source",
            timeWindowStart = kotlinx.datetime.Clock.System.now(),
            timeWindowEnd = kotlinx.datetime.Clock.System.now(),
            thresholdViolations = 10L,
            actualViolations = 3L,
            fieldName = null,
            createdAt = kotlinx.datetime.Clock.System.now(),
            notificationTargets = emptyList()
        )

        val messageJson = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

        // Process the notification
        webhookNotificationConsumer.handleIncidentNotification(messageJson)

        // Wait a bit for async processing
        Thread.sleep(100)

        // Verify webhook was called with correct authorization header
        assertThat(receivedRequests).hasSize(1)
        val request = receivedRequests.first()
        assertThat(request.method).isEqualTo("POST")
        assertThat(request.path).isEqualTo("/webhook")
        assertThat(request.headers["content-type"]).isEqualTo("application/json")

        val expectedAuth = Base64.getEncoder().encodeToString("testuser:testpass".toByteArray())
        assertThat(request.headers["authorization"]).isEqualTo("Basic $expectedAuth")
        assertThat(request.body).contains("Test Rule with Auth")
    }

    @Test
    fun `should send webhook notification with custom header credentials`() {
        // Create webhook notification target with custom header credentials
        val customHeaderCredentials = WebhookCredentials.CustomHeaderCredentials(
            headerName = "X-API-Key",
            headerValue = "secret-api-key-123"
        )

        val webhookTarget = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = tenantFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "http://localhost:8089/webhook",
            credentials = customHeaderCredentials
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(webhookTarget)
        )

        // Create incident notification message
        val incidentMessage = IncidentNotificationMessage(
            incidentId = IncidentId.from(3L),
            tenantId = tenantFixture.tenantId,
            ruleId = RuleId.from(3L),
            ruleName = "Test Rule with Custom Header",
            assetId = AssetId.from(3L),
            assetName = "Test Asset with Custom Header",
            dataSourceId = dataSourceFixture.dataSourceId,
            dataSourceName = "Test Data Source",
            timeWindowStart = kotlinx.datetime.Clock.System.now(),
            timeWindowEnd = kotlinx.datetime.Clock.System.now(),
            thresholdViolations = 10L,
            actualViolations = 7L,
            fieldName = null,
            createdAt = kotlinx.datetime.Clock.System.now(),
            notificationTargets = emptyList()
        )

        val messageJson = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

        // Process the notification
        webhookNotificationConsumer.handleIncidentNotification(messageJson)

        // Wait a bit for async processing
        Thread.sleep(100)

        // Verify webhook was called with correct custom header
        assertThat(receivedRequests).hasSize(1)
        val request = receivedRequests.first()
        assertThat(request.method).isEqualTo("POST")
        assertThat(request.path).isEqualTo("/webhook")
        assertThat(request.headers["content-type"]).isEqualTo("application/json")
        assertThat(request.headers["x-api-key"]).isEqualTo("secret-api-key-123")
        assertThat(request.body).contains("Test Rule with Custom Header")
    }

    @Test
    fun `should handle webhook failure gracefully`() {
        // Create webhook notification target
        val webhookTarget = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = tenantFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "http://localhost:8089/webhook-error",
            credentials = null
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(webhookTarget)
        )

        // Create incident notification message
        val incidentMessage = IncidentNotificationMessage(
            incidentId = IncidentId.from(4L),
            tenantId = tenantFixture.tenantId,
            ruleId = RuleId.from(4L),
            ruleName = "Test Rule Failure",
            assetId = AssetId.from(4L),
            assetName = "Test Asset Failure",
            dataSourceId = dataSourceFixture.dataSourceId,
            dataSourceName = "Test Data Source",
            timeWindowStart = kotlinx.datetime.Clock.System.now(),
            timeWindowEnd = kotlinx.datetime.Clock.System.now(),
            thresholdViolations = 10L,
            actualViolations = 1L,
            fieldName = null,
            createdAt = kotlinx.datetime.Clock.System.now(),
            notificationTargets = emptyList()
        )

        val messageJson = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

        // Process the notification - should not throw exception
        webhookNotificationConsumer.handleIncidentNotification(messageJson)

        // Wait a bit for async processing
        Thread.sleep(100)

        // Verify webhook was attempted with retries (3 attempts total: 1 initial + 2 retries)
        assertThat(receivedRequests).hasSize(3)

        // Verify all requests were to the error endpoint
        receivedRequests.forEach { request ->
            assertThat(request.method).isEqualTo("POST")
            assertThat(request.path).isEqualTo("/webhook-error")
            assertThat(request.headers["content-type"]).isEqualTo("application/json")
            assertThat(request.body).contains("Test Rule Failure")
        }
    }

    @Test
    fun `should send to multiple webhook targets`() {
        // Create multiple webhook notification targets
        val webhookTargets = listOf(
            DataSourceIncidentNotificationTarget.CreateData(
                tenantId = tenantFixture.tenantId,
                dataSourceId = dataSourceFixture.dataSourceId,
                notificationType = NotificationType.WEBHOOK,
                targetAddress = "http://localhost:8089/webhook1",
                credentials = null
            ),
            DataSourceIncidentNotificationTarget.CreateData(
                tenantId = tenantFixture.tenantId,
                dataSourceId = dataSourceFixture.dataSourceId,
                notificationType = NotificationType.WEBHOOK,
                targetAddress = "http://localhost:8089/webhook2",
                credentials = WebhookCredentials.BasicAuthenticationCredentials("user", "pass")
            )
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = webhookTargets
        )

        // Create incident notification message
        val incidentMessage = IncidentNotificationMessage(
            incidentId = IncidentId.from(5L),
            tenantId = tenantFixture.tenantId,
            ruleId = RuleId.from(5L),
            ruleName = "Test Rule Multiple",
            assetId = AssetId.from(5L),
            assetName = "Test Asset Multiple",
            dataSourceId = dataSourceFixture.dataSourceId,
            dataSourceName = "Test Data Source",
            timeWindowStart = kotlinx.datetime.Clock.System.now(),
            timeWindowEnd = kotlinx.datetime.Clock.System.now(),
            thresholdViolations = 10L,
            actualViolations = 2L,
            fieldName = null,
            createdAt = kotlinx.datetime.Clock.System.now(),
            notificationTargets = emptyList()
        )

        val messageJson = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

        // Process the notification
        webhookNotificationConsumer.handleIncidentNotification(messageJson)

        // Wait a bit for async processing
        Thread.sleep(200)

        // Verify both webhooks were called
        assertThat(receivedRequests).hasSize(2)

        val webhook1Request = receivedRequests.find { it.path == "/webhook1" }
        assertThat(webhook1Request).isNotNull
        assertThat(webhook1Request!!.method).isEqualTo("POST")
        assertThat(webhook1Request.headers["content-type"]).isEqualTo("application/json")
        assertThat(webhook1Request.headers).doesNotContainKey("authorization")

        val webhook2Request = receivedRequests.find { it.path == "/webhook2" }
        assertThat(webhook2Request).isNotNull
        assertThat(webhook2Request!!.method).isEqualTo("POST")
        assertThat(webhook2Request.headers["content-type"]).isEqualTo("application/json")

        val expectedAuth = Base64.getEncoder().encodeToString("user:pass".toByteArray())
        assertThat(webhook2Request.headers["authorization"]).isEqualTo("Basic $expectedAuth")
    }

    @Test
    fun `should not retry on 4XX client errors`() {
        // Create webhook notification target
        val webhookTarget = DataSourceIncidentNotificationTarget.CreateData(
            tenantId = tenantFixture.tenantId,
            dataSourceId = dataSourceFixture.dataSourceId,
            notificationType = NotificationType.WEBHOOK,
            targetAddress = "http://localhost:8089/webhook-client-error",
            credentials = null
        )

        dataSourceIncidentNotificationTargetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(webhookTarget)
        )

        // Create incident notification message
        val incidentMessage = IncidentNotificationMessage(
            incidentId = IncidentId.from(6L),
            tenantId = tenantFixture.tenantId,
            ruleId = RuleId.from(6L),
            ruleName = "Test Rule Client Error",
            assetId = AssetId.from(6L),
            assetName = "Test Asset Client Error",
            dataSourceId = dataSourceFixture.dataSourceId,
            dataSourceName = "Test Data Source",
            timeWindowStart = kotlinx.datetime.Clock.System.now(),
            timeWindowEnd = kotlinx.datetime.Clock.System.now(),
            thresholdViolations = 10L,
            actualViolations = 1L,
            fieldName = null,
            createdAt = kotlinx.datetime.Clock.System.now(),
            notificationTargets = emptyList()
        )

        val messageJson = Json.encodeToString(IncidentNotificationMessage.serializer(), incidentMessage)

        // Process the notification - should not throw exception
        webhookNotificationConsumer.handleIncidentNotification(messageJson)

        // Wait a bit for async processing
        Thread.sleep(100)

        // Verify webhook was attempted only once (no retries for 4XX errors)
        assertThat(receivedRequests).hasSize(1)
        val request = receivedRequests.first()
        assertThat(request.method).isEqualTo("POST")
        assertThat(request.path).isEqualTo("/webhook-client-error")
        assertThat(request.headers["content-type"]).isEqualTo("application/json")
        assertThat(request.body).contains("Test Rule Client Error")
    }
}
