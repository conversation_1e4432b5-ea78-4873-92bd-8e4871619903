package com.spars.dens.worker

import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import com.spars.dens.core.utils.testing.DensIntegrationTest
import com.spars.dens.datamodel.jooq.tables.references.TENANT
import org.jooq.DSLContext
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc

@AutoConfigureMockMvc
@DensIntegrationTest
class BaseWorkerIntegrationTest {
    @Autowired
    lateinit var dslContext: DSLContext

    @BeforeEach
    fun setup() {
        dslContext.insertInto(TENANT)
            .set(TenantFixture().createData.toJooqRecord())
            .onDuplicateKeyIgnore()
            .execute()
    }
}
