package com.spars.dens.worker.job

import com.spars.dens.core.datamodel.alert.models.AlertThreshold
import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcessId
import com.spars.dens.core.datamodel.incident.filters.IncidentFilter
import com.spars.dens.core.datamodel.incident.models.IncidentStatus
import com.spars.dens.core.datamodel.incident.repositories.IncidentRepository
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.models.RuleSetExecution
import com.spars.dens.core.datamodel.rule.models.RuleSetExecutionResultDetails
import com.spars.dens.core.datamodel.rule.models.SingleRuleExecutionResult
import com.spars.dens.core.datamodel.rule.models.ValidationRule
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.rule.repositories.RuleSetExecutionRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import com.spars.dens.worker.BaseWorkerIntegrationTest
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaInstant
import kotlinx.datetime.toKotlinInstant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import java.time.temporal.ChronoUnit
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.DurationUnit

class AlertThresholdJobIntegrationTest : BaseWorkerIntegrationTest() {

    @Autowired
    private lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    private lateinit var assetRepository: AssetRepository

    @Autowired
    private lateinit var ruleRepository: RuleRepository

    @Autowired
    private lateinit var ruleSetExecutionRepository: RuleSetExecutionRepository

    @Autowired
    private lateinit var incidentRepository: IncidentRepository

    @Autowired
    private lateinit var alertThresholdJob: AlertThresholdJob

    private val tenantFixture = TenantFixture()
    private val dataSourceFixture = DataSourceFixture()
    private val assetFixture = AssetFixture()
    private val now = Clock.System.now().toJavaInstant().truncatedTo(ChronoUnit.MINUTES).toKotlinInstant()

    // Rule with threshold of 10 violations in 1 minutes
    private val ruleWithThresholdId = RuleId.from(4428054792163321735L)
    private val ruleWithThreshold = FieldRuleFixture(
        ruleId = ruleWithThresholdId,
        assetId = assetFixture.assetId,
        fieldName = "test_field",
        ruleName = "Test Rule With Threshold",
        validationRule = ValidationRule.FieldNotNullValidationRule(),
        alertThreshold = AlertThreshold(
            numberOfViolatingRecords = 10,
            durationAmount = 1,
            durationUnit = DurationUnit.MINUTES
        )
    )

    // Rule with higher threshold of 20 violations in 1 minutes
    private val ruleWithHigherThresholdId = RuleId.from(2951944390462920225L)
    private val ruleWithHigherThreshold = FieldRuleFixture(
        ruleId = ruleWithHigherThresholdId,
        assetId = assetFixture.assetId,
        fieldName = "test_field2",
        ruleName = "Test Rule With Higher Threshold",
        validationRule = ValidationRule.FieldNotNullValidationRule(),
        alertThreshold = AlertThreshold(
            numberOfViolatingRecords = 20,
            durationAmount = 1,
            durationUnit = DurationUnit.MINUTES
        )
    )

    @BeforeEach
    override fun setup() {
        super.setup()
        dataSourceRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )
        assetRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            createData = assetFixture.createData
        )
        // Create rules
        ruleRepository.create(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(
                ruleWithThreshold.createData,
                ruleWithHigherThreshold.createData
            )
        )
    }

    @Test
    fun `test alert threshold job creates incident when threshold is exceeded`() {
        // Create rule set execution results with violations exceeding the threshold
        createRuleSetExecutions(
            ruleId = ruleWithThresholdId,
            numExecutions = 3,
            violationsPerExecution = 5, // Total 15 violations (> threshold of 10)
            createdAt = now.minus(30.seconds) // Within the 5-minute window
        )

        // Execute the job
        alertThresholdJob.findRulesAndStartCheckerJobs()

        // Verify an incident was created

        val incidents = incidentRepository.filter(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            filter = IncidentFilter().ruleId(ruleWithThresholdId)
        )

        assertThat(incidents).hasSize(1)
        val incident = incidents.first()
        assertThat(incident.rule.ruleId).isEqualTo(ruleWithThresholdId)
        assertThat(incident.status).isEqualTo(IncidentStatus.ACTIVE)

        // Check incident details
        val incidentDetails = incident.incidentDetails as? com.spars.dens.core.datamodel.incident.models.ColumnLevelRuleIncidentDetails
        assertThat(incidentDetails).isNotNull
        assertThat(incidentDetails!!.thresholdRecordsFailing).isEqualTo(10)
        assertThat(incidentDetails.actualRecordsFailing).isEqualTo(15)
        assertThat(incidentDetails.fieldName).isEqualTo("test_field")
    }

    @Test
    fun `test alert threshold job does not create incident when threshold is not exceeded`() {
        // Create rule set execution results with violations below the threshold
        createRuleSetExecutions(
            ruleId = ruleWithThresholdId,
            numExecutions = 2,
            violationsPerExecution = 3, // Total 6 violations (< threshold of 10)
            createdAt = now.minus(30.seconds) // Within the 5-minute window
        )

        // Execute the job
        alertThresholdJob.findRulesAndStartCheckerJobs()

        // Verify no incident was created
        val incidents = incidentRepository.filter(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            filter = IncidentFilter().ruleId(ruleWithThresholdId)
        )
        assertThat(incidents).isEmpty()
    }

    @Test
    fun `test alert threshold job creates incident for one rule but not another with higher threshold`() {
        // Create rule set execution results with violations for both rules
        createRuleSetExecutions(
            ruleId = ruleWithThresholdId,
            numExecutions = 3,
            violationsPerExecution = 5, // Total 15 violations (> threshold of 10)
            createdAt = now.minus(30.seconds)
        )

        createRuleSetExecutions(
            ruleId = ruleWithHigherThresholdId,
            numExecutions = 3,
            violationsPerExecution = 5, // Total 15 violations (< threshold of 20)
            createdAt = now.minus(30.seconds)
        )

        alertThresholdJob.findRulesAndStartCheckerJobs()

        // Verify an incident was created for the first rule
        val incidentsForFirstRule = incidentRepository.filter(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            filter = IncidentFilter().ruleId(ruleWithThresholdId)
        )
        assertThat(incidentsForFirstRule).hasSize(1)

        // Verify no incident was created for the second rule
        val incidentsForSecondRule = incidentRepository.filter(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            filter = IncidentFilter().ruleId(ruleWithHigherThresholdId)
        )
        assertThat(incidentsForSecondRule).isEmpty()
    }

    @Test
    fun `test alert threshold job does not create incident for violations outside time window`() {
        // Create rule set execution results with violations outside the time window
        createRuleSetExecutions(
            ruleId = ruleWithThresholdId,
            numExecutions = 3,
            violationsPerExecution = 10, // Total 30 violations (> threshold of 10)
            createdAt = now.minus(10.minutes) // Outside the 5-minute window
        )

        // Run the alert threshold checker
        alertThresholdJob.findRulesAndStartCheckerJobs()

        // Verify no incident was created
        val incidents = incidentRepository.filter(
            tenantId = tenantFixture.tenantId,
            dslContext = dslContext,
            filter = IncidentFilter().ruleId(ruleWithThresholdId)
        )
        assertThat(incidents).isEmpty()
    }

    private fun createRuleSetExecutions(
        ruleId: RuleId,
        numExecutions: Int,
        violationsPerExecution: Int,
        createdAt: Instant
    ) {
        for (i in 0 until numExecutions) {
            // Create single rule execution result
            val singleRuleResult = SingleRuleExecutionResult(
                ruleId = ruleId,
                numRowsScanned = 100,
                numRowsPassed = (100 - violationsPerExecution).toLong(),
                numRowsFailed = violationsPerExecution.toLong(),
                sampleFailingValues = listOf("sample_value_1", "sample_value_2")
            )

            // Create result details
            val resultDetails = RuleSetExecutionResultDetails(
                dataChunkId = AssetDataChunkInProcessId(999999L),
                singleRuleExecutionResults = listOf(singleRuleResult),
            )

            // Create rule set execution
            val ruleSetExecution = RuleSetExecution.CreateData(
                tenantId = tenantFixture.tenantId,
                assetId = assetFixture.assetId,
                ruleIds = listOf(ruleId),
                plannedStartTime = createdAt.toJavaInstant().truncatedTo(ChronoUnit.MINUTES).toKotlinInstant(),
                startTime = createdAt.minus(30.seconds),
                endTime = createdAt,
                numRowsScanned = 100,
                numRowsPassingAllRules = (100 - violationsPerExecution).toLong(),
                numRowsFailingAnyRule = violationsPerExecution.toLong(),
                numRowsSkipped = 0,
                isCompleteSchemaViolation = false,
                overallDataQualityScore = (100 - violationsPerExecution).toDouble(),
                resultDetails = resultDetails,
                createdAt = createdAt,
            )

            ruleSetExecutionRepository.create(
                tenantId = tenantFixture.tenantId,
                dslContext = dslContext,
                createData = ruleSetExecution
            )
        }
    }
}
