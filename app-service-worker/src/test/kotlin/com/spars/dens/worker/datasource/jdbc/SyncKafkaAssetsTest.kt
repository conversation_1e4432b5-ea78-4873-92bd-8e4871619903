package com.spars.dens.worker.datasource.jdbc

import com.spars.dens.core.datamodel.asset.models.Schema
import com.spars.dens.core.datamodel.dataformat.models.DataFormat
import com.spars.dens.core.datamodel.kafka.models.KafkaSchemaType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class SyncKafkaAssetsTest {

    @Test
    fun `test parseSchemaFromRegistry with ProtoBuf schema`() {
        // Given
        val protoSchemaString = """
            syntax = "proto3";
            package com.datapakt.orders;

            import "google/protobuf/timestamp.proto";

            message OrderEvent {
              string order_id = 1;
              string customer_id = 2;
              OrderStatus status = 3;
              double total_amount = 4;
              int32 item_count = 5;
              google.protobuf.Timestamp created_at = 6;
              string order_date = 7;
              ShippingInfo shipping_info = 8;
              repeated string product_ids = 9;
              map<string, string> attributes = 10;
              bool is_gift = 14;
              string gift_message = 15;
              float discount_percentage = 16;
            }

            enum OrderStatus {
              PENDING = 0;
              CONFIRMED = 1;
              SHIPPED = 2;
              DELIVERED = 3;
              CANCELLED = 4;
            }

            message ShippingInfo {
              string address = 1;
              string city = 2;
              string postal_code = 3;
              string country = 4;
              ShippingMethod method = 5;
              google.protobuf.Timestamp estimated_delivery = 6;
            }

            enum ShippingMethod {
              STANDARD = 0;
              EXPRESS = 1;
              OVERNIGHT = 2;
            }
        """.trimIndent()

        // When
        val (schema, dataFormat) = parseSchemaFromRegistry(protoSchemaString, "PROTOBUF", "test-topic-value", 1, 123)

        // Then
        assertNotNull(schema)
        assertNotNull(dataFormat)
        assertEquals(DataFormat.PROTOBUF, dataFormat)
        assertTrue(schema is Schema.KafkaSchema)

        // Verify the schema content
        val kafkaSchema = schema as Schema.KafkaSchema
        assertEquals(protoSchemaString, kafkaSchema.schema)
        assertEquals("test-topic-value", kafkaSchema.subject)
        assertEquals(KafkaSchemaType.PROTOBUF, kafkaSchema.schemaType)
        assertEquals(1, kafkaSchema.version)
        assertEquals(123, kafkaSchema.id)
    }

    @Test
    fun `test parseSchemaFromRegistry with Avro schema`() {
        // Given
        val avroSchemaString = """
            {
              "type": "record",
              "name": "UserEvent",
              "namespace": "com.datapakt",
              "fields": [
                {"name": "eventId", "type": "string"},
                {"name": "userId", "type": "string"},
                {"name": "timestamp", "type": {"type": "long", "logicalType": "timestamp-millis"}}
              ]
            }
        """.trimIndent()

        // When
        val (schema, dataFormat) = parseSchemaFromRegistry(avroSchemaString, "AVRO", "user-events-value", 2, 456)

        // Then
        assertNotNull(schema)
        assertNotNull(dataFormat)
        assertEquals(DataFormat.AVRO, dataFormat)
        assertTrue(schema is Schema.KafkaSchema)

        // Verify the schema content
        val kafkaSchema = schema as Schema.KafkaSchema
        assertEquals(avroSchemaString, kafkaSchema.schema)
        assertEquals("user-events-value", kafkaSchema.subject)
        assertEquals(KafkaSchemaType.AVRO, kafkaSchema.schemaType)
        assertEquals(2, kafkaSchema.version)
        assertEquals(456, kafkaSchema.id)
    }

    @Test
    fun `test parseSchemaFromRegistry with JSON schema`() {
        // Given
        val jsonSchemaString = """
            {
              "type": "object",
              "properties": {
                "sensorId": {"type": "string"},
                "temperature": {"type": "number"},
                "timestamp": {"type": "string", "format": "date-time"}
              },
              "required": ["sensorId", "temperature", "timestamp"]
            }
        """.trimIndent()

        // When
        val (schema, dataFormat) = parseSchemaFromRegistry(jsonSchemaString, "JSON", "sensor-data-value", 3, 789)

        // Then
        assertNotNull(schema)
        assertNotNull(dataFormat)
        assertEquals(DataFormat.JSON, dataFormat)
        assertTrue(schema is Schema.KafkaSchema)

        // Verify the schema content
        val kafkaSchema = schema as Schema.KafkaSchema
        assertEquals(jsonSchemaString, kafkaSchema.schema)
        assertEquals("sensor-data-value", kafkaSchema.subject)
        assertEquals(KafkaSchemaType.JSON, kafkaSchema.schemaType)
        assertEquals(3, kafkaSchema.version)
        assertEquals(789, kafkaSchema.id)
    }

    @Test
    fun `test parseSchemaFromRegistry with unsupported schema type`() {
        // Given
        val schemaString = "some schema content"

        // When
        val (schema, dataFormat) = parseSchemaFromRegistry(schemaString, "UNSUPPORTED", "test-subject", 1, 1)

        // Then
        assertNull(schema)
        assertNull(dataFormat)
    }

    @Test
    fun `test KafkaSchema with nullable version and id`() {
        // Given - simulating a schema created via API without registry metadata
        val kafkaSchema = Schema.KafkaSchema(
            schema = """{"type": "string"}""",
            subject = "manual-topic-value",
            schemaType = KafkaSchemaType.JSON,
            version = null,
            id = null
        )

        // Then
        assertEquals("""{"type": "string"}""", kafkaSchema.schema)
        assertEquals("manual-topic-value", kafkaSchema.subject)
        assertEquals(KafkaSchemaType.JSON, kafkaSchema.schemaType)
        assertNull(kafkaSchema.version)
        assertNull(kafkaSchema.id)
    }

    @Test
    fun `test KafkaSchema with nullable subject`() {
        // Given - simulating a schema created via CreateKafkaTopicAssetDto without subject
        val kafkaSchema = Schema.KafkaSchema(
            schema = """{"type": "string"}""",
            subject = null,
            schemaType = KafkaSchemaType.JSON,
            version = null,
            id = null
        )

        // Then
        assertEquals("""{"type": "string"}""", kafkaSchema.schema)
        assertNull(kafkaSchema.subject)
        assertEquals(KafkaSchemaType.JSON, kafkaSchema.schemaType)
        assertNull(kafkaSchema.version)
        assertNull(kafkaSchema.id)
    }

    // Helper method to simulate the private method from SyncDataSourceAssetsListener
    private fun parseSchemaFromRegistry(
        schemaString: String,
        schemaType: String,
        subject: String,
        version: Int?,
        id: Int?
    ): Pair<Schema?, DataFormat?> {
        return try {
            when (schemaType.uppercase()) {
                "AVRO" -> {
                    val kafkaSchema = Schema.KafkaSchema(
                        schema = schemaString,
                        subject = subject,
                        schemaType = KafkaSchemaType.AVRO,
                        version = version,
                        id = id
                    )
                    Pair(kafkaSchema, DataFormat.AVRO)
                }
                "PROTOBUF" -> {
                    val kafkaSchema = Schema.KafkaSchema(
                        schema = schemaString,
                        subject = subject,
                        schemaType = KafkaSchemaType.PROTOBUF,
                        version = version,
                        id = id
                    )
                    Pair(kafkaSchema, DataFormat.PROTOBUF)
                }
                "JSON" -> {
                    val kafkaSchema = Schema.KafkaSchema(
                        schema = schemaString,
                        subject = subject,
                        schemaType = KafkaSchemaType.JSON,
                        version = version,
                        id = id
                    )
                    Pair(kafkaSchema, DataFormat.JSON)
                }
                else -> {
                    Pair(null, null)
                }
            }
        } catch (ex: Exception) {
            Pair(null, null)
        }
    }
}
