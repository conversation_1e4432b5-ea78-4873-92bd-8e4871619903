package com.spars.dens.worker

import org.apache.kafka.clients.admin.AdminClient
import org.apache.kafka.clients.admin.CreateTopicsOptions
import org.apache.kafka.clients.admin.DeleteTopicsOptions
import org.apache.kafka.clients.admin.ListTopicsOptions
import org.apache.kafka.clients.admin.NewTopic
import org.apache.kafka.common.config.SaslConfigs
import org.apache.kafka.common.config.TopicConfig
import org.apache.kafka.common.errors.TopicExistsException
import java.util.Properties
import java.util.concurrent.TimeUnit

fun main() {
    println("Hello World, This is Test W")
    listKafkaTopics()
    println("\nTrying plaintext connection as fallback:")
    listKafkaTopicsPlaintext()

    println("\nCreating a new topic:")
    createKafkaTopic("labor-survey-result", 3, 1)

    println("\nListing topics after creation:")
    listKafkaTopics()
}

fun createKafkaTopic(topicName: String, partitions: Int, replicationFactor: Short) {
    println("Creating Kafka topic: $topicName with $partitions partitions and replication factor $replicationFactor")

    val properties = Properties().apply {
        // Basic connection properties
        put("bootstrap.servers", "localhost:9092")
        put("client.id", "topic-creator")

        // Connection settings
        put("request.timeout.ms", "20000")
        put("connections.max.idle.ms", "10000")
        put("retry.backoff.ms", "500")
    }

    try {
        AdminClient.create(properties).use { adminClient ->
            // Check if topic exists
            val existingTopics = adminClient.listTopics(ListTopicsOptions().timeoutMs(10000))
                .names().get(15, TimeUnit.SECONDS)

            if (existingTopics.contains(topicName)) {
                println("Topic '$topicName' already exists. Deleting it first...")

                // Delete the topic
                val deleteOptions = DeleteTopicsOptions().timeoutMs(30000)
                val deleteResult = adminClient.deleteTopics(listOf(topicName), deleteOptions)

                try {
                    // Wait for deletion to complete
                    deleteResult.all().get(30, TimeUnit.SECONDS)
                    println("Topic '$topicName' deleted successfully")

                    // Small delay to ensure the deletion is processed
                    Thread.sleep(2000)
                } catch (ex: Exception) {
                    println("Warning: Failed to delete topic: ${ex.message}")
                    // Continue anyway, as the create might still succeed
                }
            }

            // Create topic configuration
            val configs = mapOf(
                TopicConfig.CLEANUP_POLICY_CONFIG to TopicConfig.CLEANUP_POLICY_COMPACT,
                TopicConfig.RETENTION_MS_CONFIG to "604800000" // 7 days in milliseconds
            )

            // Create new topic
            val newTopic = NewTopic(topicName, partitions, replicationFactor).configs(configs)

            // Set options
            val options = CreateTopicsOptions().timeoutMs(30000)

            try {
                // Create the topic
                val createTopicsResult = adminClient.createTopics(listOf(newTopic), options)

                // Wait for completion
                createTopicsResult.all().get(30, TimeUnit.SECONDS)
                println("Topic '$topicName' created successfully")
            } catch (ex: Exception) {
                // If the topic already exists (race condition), that's fine
                if (ex.cause is TopicExistsException) {
                    println("Topic '$topicName' already exists (race condition)")
                } else {
                    throw ex
                }
            }
        }
    } catch (ex: Exception) {
        println("Failed to create Kafka topic: ${ex.message}")
        ex.printStackTrace()
    }
}

fun listKafkaTopics() {
    println("Connecting to Kafka and listing topics...")

    val properties = Properties().apply {
        // Basic connection properties
        put("bootstrap.servers", "localhost:9094")
        put("client.id", "topic-lister")

        // Authentication configuration
        put("security.protocol", "SASL_PLAINTEXT")
        put("sasl.mechanism", "PLAIN")
        put(
            SaslConfigs.SASL_JAAS_CONFIG,
            "org.apache.kafka.common.security.plain.PlainLoginModule required " +
                "username=\"user\" " +
                "password=\"password\";"
        )
    }

    try {
        AdminClient.create(properties).use { adminClient ->
            // Set a reasonable timeout
            val options = ListTopicsOptions().apply {
                listInternal(true) // Include internal topics
                timeoutMs(10000) // 10 second timeout
            }

            // List topics
            val topicsListingFuture = adminClient.listTopics(options)
            val topics = topicsListingFuture.names().get(15, TimeUnit.SECONDS)

            println("Found ${topics.size} topics:")
            topics.forEach { topic ->
                println(" - $topic")
            }
        }
    } catch (ex: Exception) {
        println("Failed to connect to Kafka: ${ex.message}")
        ex.printStackTrace()
    }
}

fun listKafkaTopicsPlaintext() {
    println("Connecting to Kafka using PLAINTEXT and listing topics...")

    val properties = Properties().apply {
        // Basic connection properties - using plaintext port
        put("bootstrap.servers", "localhost:9092")
        put("client.id", "topic-lister-plaintext")

        // Connection settings
        put("request.timeout.ms", "20000")
        put("connections.max.idle.ms", "10000")
        put("retry.backoff.ms", "500")
    }

    try {
        AdminClient.create(properties).use { adminClient ->
            val options = ListTopicsOptions().timeoutMs(20000)
            val topics = adminClient.listTopics(options).names().get(30, TimeUnit.SECONDS)

            println("Found ${topics.size} topics:")
            topics.forEach { topic ->
                println(" - $topic")
            }
        }
    } catch (ex: Exception) {
        println("Failed to connect to Kafka (plaintext): ${ex.message}")
        ex.printStackTrace()
    }
}
/*
fun getSchemaTypeForSubject(subject: String, registryUrl: String = "http://localhost:8081"): String {
    val client: SchemaRegistryClient = CachedSchemaRegistryClient(registryUrl, 10)
    val metadata: SchemaMetadata = client.getLatestSchemaMetadata(subject)

    // schemaType is not included in SchemaMetadata; must use the RESTEntities API
    metadata.schemaType

}
 */
