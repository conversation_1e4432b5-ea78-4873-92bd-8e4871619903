package com.spars.dens.worker.notification

import com.spars.dens.core.datamodel.notification.models.NotificationType
import com.spars.dens.worker.BaseWorkerIntegrationTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class IncidentNotificationIntegrationTest : BaseWorkerIntegrationTest() {

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var smsService: SmsService

    @Autowired
    private lateinit var webhookService: WebhookService

    @Autowired
    private lateinit var emailNotificationConsumer: EmailNotificationConsumer

    @Autowired
    private lateinit var smsNotificationConsumer: SmsNotificationConsumer

    @Autowired
    private lateinit var webhookNotificationConsumer: WebhookNotificationConsumer

    @Test
    fun `should have all notification services properly configured`() {
        // Verify that all notification services are properly injected
        assertThat(emailService).isNotNull
        assertThat(smsService).isNotNull
        assertThat(webhookService).isNotNull
    }

    @Test
    fun `should have all notification consumers properly configured`() {
        // Verify that all notification consumers are properly injected
        assertThat(emailNotificationConsumer).isNotNull
        assertThat(smsNotificationConsumer).isNotNull
        assertThat(webhookNotificationConsumer).isNotNull
    }

    @Test
    fun `should have all notification types defined`() {
        // Verify that all notification types are available
        val notificationTypes = NotificationType.values()
        assertThat(notificationTypes).contains(
            NotificationType.EMAIL,
            NotificationType.SMS,
            NotificationType.WEBHOOK
        )
        assertThat(notificationTypes).hasSize(3)
    }
}
