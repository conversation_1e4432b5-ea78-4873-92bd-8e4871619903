package com.spars.dens.worker.dataquality

import com.spars.dens.core.datamodel.asset.fixtures.AssetFixture
import com.spars.dens.core.datamodel.asset.fixtures.AssetSchemaVersionFixture
import com.spars.dens.core.datamodel.asset.models.AssetId
import com.spars.dens.core.datamodel.asset.models.AssetSchemaVersionId
import com.spars.dens.core.datamodel.asset.models.AssetType
import com.spars.dens.core.datamodel.asset.models.AssetTypeSpecificSettings
import com.spars.dens.core.datamodel.asset.models.Column
import com.spars.dens.core.datamodel.asset.models.FieldType
import com.spars.dens.core.datamodel.asset.models.Schema
import com.spars.dens.core.datamodel.asset.repositories.AssetRepository
import com.spars.dens.core.datamodel.dataformat.models.DataFormat
import com.spars.dens.core.datamodel.dataformat.models.DataFormatSpecificSettings
import com.spars.dens.core.datamodel.datasource.fixtures.DataSourceFixture
import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSourceType
import com.spars.dens.core.datamodel.datasource.repositories.DataSourceRepository
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcess
import com.spars.dens.core.datamodel.file.models.AssetDataChunkInProcessId
import com.spars.dens.core.datamodel.file.repositories.AssetDataChunkInProcessRepository
import com.spars.dens.core.datamodel.folder.models.FileSystemProtocol
import com.spars.dens.core.datamodel.process.ProcessStatus
import com.spars.dens.core.datamodel.rule.filters.RuleSetExecutionFilter
import com.spars.dens.core.datamodel.rule.fixtures.FieldRuleFixture
import com.spars.dens.core.datamodel.rule.models.FileChunkDetails
import com.spars.dens.core.datamodel.rule.models.RuleId
import com.spars.dens.core.datamodel.rule.models.ValidationRule
import com.spars.dens.core.datamodel.rule.models.comparable.NumberValue
import com.spars.dens.core.datamodel.rule.repositories.RuleRepository
import com.spars.dens.core.datamodel.rule.repositories.RuleSetExecutionRepository
import com.spars.dens.core.datamodel.tenant.fixtures.TenantFixture
import com.spars.dens.worker.BaseWorkerIntegrationTest
import kotlinx.datetime.Clock
import org.apache.commons.vfs2.FileSystemOptions
import org.apache.commons.vfs2.Selectors
import org.apache.commons.vfs2.auth.StaticUserAuthenticator
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder
import org.apache.commons.vfs2.impl.StandardFileSystemManager
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource
import org.springframework.transaction.annotation.Transactional
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.util.zip.GZIPOutputStream

@TestPropertySource(
    properties = [
        "data-quality-job.multi-threading.compressed-file-threshold-mb=1",
        "data-quality-job.multi-threading.uncompressed-file-threshold-mb=1"
    ]
)
class DataFolderCSVColumnLevelRulesRunnerMultiThreadedIntegrationTest : BaseWorkerIntegrationTest() {

    @Autowired
    private lateinit var dataSourceRepository: DataSourceRepository

    @Autowired
    private lateinit var assetRepository: AssetRepository

    @Autowired
    private lateinit var ruleRepository: RuleRepository

    @Autowired
    private lateinit var ruleSetExecutionRepository: RuleSetExecutionRepository

    @Autowired
    private lateinit var assetDataChunkInProcessRepository: AssetDataChunkInProcessRepository

    @Autowired
    private lateinit var dataFolderCSVColumnLevelRulesRunner: DataFolderCSVColumnLevelRulesRunner

    private lateinit var fsManager: StandardFileSystemManager
    private lateinit var fsOptions: FileSystemOptions

    private val sftpDataSourceConnectionSettings = ConnectionSettings.FolderConnectionSettings(
        protocol = FileSystemProtocol.SFTP,
        host = "localhost",
        port = 2222,
        userName = "user",
        password = "password"
    )

    private val dataSourceFixture = DataSourceFixture(
        dataSourceType = DataSourceType.FOLDER,
        connectionSettings = sftpDataSourceConnectionSettings
    )

    private val testFolderName = "/data/multi_thread_test"
    private val testFileName = "large_test_file.csv"
    private val dataChunkId = AssetDataChunkInProcessId.from(98765432123456789L)
    private val ruleId1 = RuleId.from(1111111111111111111L)
    private val ruleId2 = RuleId.from(2222222222222222222L)

    private val assetFixture = AssetFixture(
        assetType = AssetType.DATA_FOLDER,
        dataSourceId = dataSourceFixture.dataSourceId,
        currentSchemaFixture = AssetSchemaVersionFixture(
            schema = Schema.CsvSchema(
                columns = listOf(
                    Column(
                        name = "id",
                        type = FieldType.NUMBER,
                        required = null,
                        typeInSource = null,
                        format = null,
                        constraints = null
                    ),
                    Column(
                        name = "name",
                        type = FieldType.TEXT,
                        required = null,
                        typeInSource = null,
                        format = null,
                        constraints = null
                    ),
                    Column(
                        name = "score",
                        type = FieldType.NUMBER,
                        required = null,
                        typeInSource = null,
                        format = null,
                        constraints = null
                    )
                )
            ),
            dataFormat = DataFormat.CSV,
            dataFormatSpecificSettings = DataFormatSpecificSettings.CsvDataFormatSettings(
                delimiter = ",",
                header = true,
                quote = "\"",
                escape = "\\"
            )
        ),
        assetTypeSpecificSettings = AssetTypeSpecificSettings.DataFolderAssetSettings(
            baseFolder = testFolderName,
            baseInstant = null,
            fileNameFilter = null,
            compressed = false
        )
    )

    @BeforeEach
    override fun setup() {
        super.setup()
        // Configure SFTP options
        val authenticator = StaticUserAuthenticator(null, sftpDataSourceConnectionSettings.userName, sftpDataSourceConnectionSettings.password)
        fsOptions = FileSystemOptions()
        // Set authenticator
        DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(fsOptions, authenticator)

        // Configure FTP settings
        FtpFileSystemConfigBuilder.getInstance().setPassiveMode(fsOptions, true)

        // Configure SFTP settings
        SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(fsOptions, "no")

        // Initialize file system manager
        fsManager = StandardFileSystemManager()
        fsManager.init()

        // Create test folder and large CSV file
        val baseUri = sftpDataSourceConnectionSettings.getBaseFolderURIString()
        val cleanPath = testFolderName.removePrefix("/")
        val folderURI = "$baseUri/$cleanPath"

        val testFolder = fsManager.resolveFile(folderURI, fsOptions)
        testFolder.createFolder()

        val testFileObject = testFolder.resolveFile(testFileName)
        val testFilePath = testFileObject.name.path

        // Generate a large CSV file that will trigger multi-threading (> 1 MB)
        val csvContent = generateLargeCSVContent(50000) // 50K rows to ensure file > 1 MB

        testFileObject.content.outputStream.use { out ->
            ByteArrayInputStream(csvContent.toByteArray()).copyTo(out)
        }

        // Create data source, asset, and rules
        setupTestData(testFilePath, csvContent.length.toLong())
    }

    private fun generateLargeCSVContent(numRows: Int): String {
        val header = "id,name,score"
        val rows = (1..numRows).map { i ->
            // Create longer names to increase file size
            val name = if (i % 10 == 0) "" else "User${i}_with_longer_name_to_increase_file_size_significantly"
            val score = if (i % 20 == 0) -1 else (i % 100) // 5% invalid scores
            "$i,$name,$score"
        }

        // Build CSV content: header + newline + rows joined by newlines (no trailing newline)
        val allLines = listOf(header) + rows
        val content = allLines.joinToString("\n")

        // Log the file size and line count to verify
        val sizeInBytes = content.toByteArray().size
        val sizeInMB = sizeInBytes / (1024.0 * 1024.0)
        val lineCount = content.count { it == '\n' } + 1 // +1 because last line doesn't end with \n
        println("Generated CSV content: $numRows data rows + 1 header = $lineCount total lines, $sizeInBytes bytes (${String.format("%.2f", sizeInMB)} MB)")

        return content
    }

    private fun setupTestData(testFilePath: String, fileSize: Long) {
        // Create data source
        dataSourceRepository.create(
            tenantId = dataSourceFixture.tenantId,
            dslContext = dslContext,
            createData = dataSourceFixture.createData
        )

        // Create asset
        assetRepository.create(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            createData = assetFixture.createData
        )

        // Create rules
        val nameNotNullRule = FieldRuleFixture(
            ruleId = ruleId1,
            assetId = assetFixture.assetId,
            fieldName = "name",
            ruleName = "Name must not be null",
            validationRule = ValidationRule.FieldNotNullValidationRule()
        )

        val scoreRangeRule = FieldRuleFixture(
            ruleId = ruleId2,
            assetId = assetFixture.assetId,
            fieldName = "score",
            ruleName = "Score must be between 0 and 100",
            validationRule = ValidationRule.FieldRangeValidationRule(
                minValue = NumberValue(0),
                maxValue = NumberValue(100),
                minInclusive = true,
                maxInclusive = true
            )
        )

        ruleRepository.create(
            tenantId = nameNotNullRule.tenantId,
            dslContext = dslContext,
            dataItems = listOf(nameNotNullRule.createData, scoreRangeRule.createData)
        )

        // Create file chunk entry
        val fileAssetDataChunkCreateData = AssetDataChunkInProcess.CreateData(
            tenantId = assetFixture.tenantId,
            assetDataChunkInProcessId = dataChunkId,
            assetId = assetFixture.assetId,
            dataChunkDetails = FileChunkDetails(
                filePath = testFilePath,
                fileSize = fileSize,
            ),
            processedStatus = ProcessStatus.QUEUED,
            createdAt = Clock.System.now(),
            numItems = null,
            processedStatusDetails = null,
            numTrials = 0
        )

        assetDataChunkInProcessRepository.create(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(fileAssetDataChunkCreateData)
        )
    }

    @AfterEach
    fun cleanup() {
        try {
            val baseUri = sftpDataSourceConnectionSettings.getBaseFolderURIString()
            val cleanPath = testFolderName.removePrefix("/")
            val folderURI = "$baseUri/$cleanPath"
            val testFolder = fsManager.resolveFile(folderURI, fsOptions)
            if (testFolder.exists()) {
                testFolder.delete(Selectors.SELECT_ALL)
            }
        } catch (e: Exception) {
            println("Error cleaning up SFTP files: ${e.message}")
        } finally {
            fsManager.close()
        }
    }

    @Test
    @Transactional
    fun `test multi-threaded processing with large CSV file`() {
        // Create message for processing
        val message = com.spars.dens.core.datamodel.rule.models.ColumnLevelRulesMessage(
            tenantId = TenantFixture().tenantId,
            assetId = assetFixture.assetId,
            ruleIds = listOf(ruleId1, ruleId2),
            plannedStartTime = Clock.System.now(),
            dataChunkId = dataChunkId
        )

        // Process the file (this should trigger multi-threading due to low threshold)
        dataFolderCSVColumnLevelRulesRunner.runRules(message)

        // Verify the file was processed successfully
        val updatedFileChunk = assetDataChunkInProcessRepository.getByIds(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(dataChunkId),
            ignoreDeleted = false
        ).first()

        assertThat(updatedFileChunk.processedStatus).isEqualTo(ProcessStatus.COMPLETED)

        // Verify rule set execution is created
        val ruleSetExecutions = ruleSetExecutionRepository.filter(
            tenantId = assetFixture.tenantId,
            dslContext = dslContext,
            filter = RuleSetExecutionFilter().assetIdIn(listOf(assetFixture.assetId))
        )
        assertThat(ruleSetExecutions).hasSize(1)
        val execution = ruleSetExecutions[0]
        assertThat(execution.ruleIds).containsExactlyInAnyOrder(ruleId1, ruleId2)
        assertThat(execution.numRowsScanned).isEqualTo(50000L) // Total rows in CSV

        // Verify data quality metrics
        // With 50K rows: 10% null names (5K), 5% invalid scores (2.5K)
        // Some rows have both violations, so failing rows < 7.5K
        assertThat(execution.numRowsFailingAnyRule).isGreaterThan(0L)
        assertThat(execution.numRowsPassingAllRules).isGreaterThan(0L)
        assertThat(execution.numRowsPassingAllRules!! + execution.numRowsFailingAnyRule!!).isEqualTo(50000L)
        assertThat(execution.overallDataQualityScore).isBetween(0.0, 100.0)

        // Verify result details contain individual rule results
        assertThat(execution.resultDetails.singleRuleExecutionResults).hasSize(2)
        assertThat(execution.resultDetails.dataChunkId).isEqualTo(dataChunkId)
    }

    @Test
    @Transactional
    fun `test multi-threaded processing with large compressed CSV file`() {
        // Create a compressed version of the test
        val compressedTestFileName = "large_compressed_test_file.csv.gz"
        val compressedDataChunkId = AssetDataChunkInProcessId.from(98765432123456790L)

        // Create compressed asset fixture
        val compressedAssetFixture = assetFixture.copy(
            assetId = AssetId.from(98765432123456790L),
            assetName = "Compressed Test Asset",
            assetTypeSpecificSettings = AssetTypeSpecificSettings.DataFolderAssetSettings(
                baseFolder = testFolderName,
                baseInstant = null,
                fileNameFilter = null,
                compressed = true // Enable compression
            ),
            currentSchemaFixture = assetFixture.currentSchemaFixture.copy(
                assetId = AssetId.from(98765432123456790L),
                assetSchemaVersionId = AssetSchemaVersionId.from(98765432123456790L)
            )
        )

        // Create compressed file
        val baseUri = sftpDataSourceConnectionSettings.getBaseFolderURIString()
        val cleanPath = testFolderName.removePrefix("/")
        val folderURI = "$baseUri/$cleanPath"
        val testFolder = fsManager.resolveFile(folderURI, fsOptions)
        val compressedFileObject = testFolder.resolveFile(compressedTestFileName)
        val compressedFilePath = compressedFileObject.name.path

        // Generate and compress CSV content
        val csvContent = generateLargeCSVContent(100000) // 100K rows for compressed file to ensure it exceeds 1MB when compressed
        val compressedContent = compressContent(csvContent)

        compressedFileObject.content.outputStream.use { out ->
            ByteArrayInputStream(compressedContent).copyTo(out)
        }

        // Create compressed asset
        assetRepository.create(
            tenantId = compressedAssetFixture.tenantId,
            dslContext = dslContext,
            createData = compressedAssetFixture.createData
        )

        // Create rules for the compressed asset
        val compressedRuleId1 = RuleId.from(98765432123456791L)
        val compressedRuleId2 = RuleId.from(98765432123456792L)

        val compressedNameNotNullRule = FieldRuleFixture(
            ruleId = compressedRuleId1,
            assetId = compressedAssetFixture.assetId,
            fieldName = "name",
            ruleName = "Name must not be null",
            validationRule = ValidationRule.FieldNotNullValidationRule()
        )

        val compressedScoreRangeRule = FieldRuleFixture(
            ruleId = compressedRuleId2,
            assetId = compressedAssetFixture.assetId,
            fieldName = "score",
            ruleName = "Score must be between 0 and 100",
            validationRule = ValidationRule.FieldRangeValidationRule(
                minValue = NumberValue(0),
                maxValue = NumberValue(100),
                minInclusive = true,
                maxInclusive = true
            )
        )

        ruleRepository.create(
            tenantId = compressedAssetFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(compressedNameNotNullRule.createData, compressedScoreRangeRule.createData)
        )

        // Create file chunk entry for compressed file
        val compressedFileAssetDataChunkCreateData = AssetDataChunkInProcess.CreateData(
            tenantId = compressedAssetFixture.tenantId,
            assetDataChunkInProcessId = compressedDataChunkId,
            assetId = compressedAssetFixture.assetId,
            dataChunkDetails = FileChunkDetails(
                filePath = compressedFilePath,
                fileSize = compressedContent.size.toLong(),
            ),
            processedStatus = ProcessStatus.QUEUED,
            createdAt = Clock.System.now(),
            numItems = null,
            processedStatusDetails = null,
            numTrials = 0
        )

        assetDataChunkInProcessRepository.create(
            tenantId = compressedAssetFixture.tenantId,
            dslContext = dslContext,
            dataItems = listOf(compressedFileAssetDataChunkCreateData)
        )

        // Create message for processing compressed file
        val compressedMessage = com.spars.dens.core.datamodel.rule.models.ColumnLevelRulesMessage(
            tenantId = compressedAssetFixture.tenantId,
            assetId = compressedAssetFixture.assetId,
            ruleIds = listOf(compressedRuleId1, compressedRuleId2),
            plannedStartTime = Clock.System.now(),
            dataChunkId = compressedDataChunkId
        )

        // Process the compressed file (this should trigger multi-threading)
        dataFolderCSVColumnLevelRulesRunner.runRules(compressedMessage)

        // Verify the compressed file was processed successfully
        val updatedCompressedFileChunk = assetDataChunkInProcessRepository.getByIds(
            tenantId = compressedAssetFixture.tenantId,
            dslContext = dslContext,
            ids = listOf(compressedDataChunkId),
            ignoreDeleted = false
        ).first()

        assertThat(updatedCompressedFileChunk.processedStatus).isEqualTo(ProcessStatus.COMPLETED)

        // Verify rule set execution is created for compressed file
        val compressedRuleSetExecutions = ruleSetExecutionRepository.filter(
            tenantId = compressedAssetFixture.tenantId,
            dslContext = dslContext,
            filter = RuleSetExecutionFilter().assetIdIn(listOf(compressedAssetFixture.assetId))
        )
        assertThat(compressedRuleSetExecutions).hasSize(1)
        val compressedExecution = compressedRuleSetExecutions[0]
        assertThat(compressedExecution.ruleIds).containsExactlyInAnyOrder(compressedRuleId1, compressedRuleId2)

        // Verify data quality metrics for compressed file
        // With 100K rows: 10% null names (10K), 5% invalid scores (5K)
        assertThat(compressedExecution.numRowsFailingAnyRule).isGreaterThan(0L)
        assertThat(compressedExecution.numRowsPassingAllRules).isGreaterThan(0L)
        assertThat(compressedExecution.numRowsPassingAllRules!! + compressedExecution.numRowsFailingAnyRule!!).isEqualTo(100000L)
        assertThat(compressedExecution.overallDataQualityScore).isBetween(0.0, 100.0)

        // Verify result details contain individual rule results
        assertThat(compressedExecution.resultDetails.singleRuleExecutionResults).hasSize(2)
        assertThat(compressedExecution.resultDetails.dataChunkId).isEqualTo(compressedDataChunkId)
    }

    private fun compressContent(content: String): ByteArray {
        val byteArrayOutputStream = ByteArrayOutputStream()
        GZIPOutputStream(byteArrayOutputStream).use { gzipOut ->
            gzipOut.write(content.toByteArray())
        }
        val compressedBytes = byteArrayOutputStream.toByteArray()

        // Log compression info
        val originalSize = content.toByteArray().size
        val compressedSize = compressedBytes.size
        val compressionRatio = (compressedSize.toDouble() / originalSize) * 100
        println("Compressed $originalSize bytes to $compressedSize bytes (${String.format("%.1f", compressionRatio)}% of original)")

        return compressedBytes
    }
}
