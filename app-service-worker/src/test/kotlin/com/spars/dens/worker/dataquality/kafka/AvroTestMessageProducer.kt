package com.spars.dens.worker.dataquality.kafka

import io.confluent.kafka.serializers.KafkaAvroSerializer
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.util.*

/**
 * Helper class for producing AVRO messages to Kafka topics in tests.
 * Uses Confluent's AVRO serializer with schema registry integration.
 */
class AvroTestMessageProducer(
    private val bootstrapServers: String,
    private val schemaRegistryUrl: String
) {
    private val logger: Logger = LoggerFactory.getLogger(AvroTestMessageProducer::class.java)
    private val producer: KafkaProducer<String, GenericRecord>

    init {
        val props = Properties().apply {
            put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers)
            put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer::class.java.name)
            put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, KafkaAvroSerializer::class.java.name)
            put("schema.registry.url", schemaRegistryUrl)
            put(ProducerConfig.ACKS_CONFIG, "all")
            put(ProducerConfig.RETRIES_CONFIG, 3)
            put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true)
        }
        producer = KafkaProducer(props)
    }

    /**
     * Loads AVRO schema from test resources.
     */
    fun loadSchemaFromResources(schemaFileName: String): Schema {
        val schemaStream = this.javaClass.classLoader.getResourceAsStream("schemas/$schemaFileName")
            ?: throw IllegalArgumentException("Schema file not found: schemas/$schemaFileName")

        return Schema.Parser().parse(schemaStream)
    }

    /**
     * Produces AVRO sensor reading messages to the specified topic.
     */
    fun produceSensorReadingMessages(topicName: String) {
        val avroSchema = loadSchemaFromResources("sensor-reading.avsc")
        val messages = createSensorReadingMessages(avroSchema)

        messages.forEachIndexed { index, record ->
            val producerRecord = ProducerRecord(topicName, "sensor-key-$index", record)
            try {
                val metadata = producer.send(producerRecord).get()
                logger.info("Produced AVRO message to topic: ${metadata.topic()}, partition: ${metadata.partition()}, offset: ${metadata.offset()}")
            } catch (e: Exception) {
                logger.error("Failed to produce AVRO message", e)
                throw e
            }
        }
        producer.flush()
    }

    private fun createSensorReadingMessages(avroSchema: Schema): List<GenericRecord> {
        return listOf(
            // Valid messages (should pass all rules)
            createSensorReading(avroSchema, "TEMP001", 22.5, 65.0, 85, 1640995200000L),
            createSensorReading(avroSchema, "TEMP002", 18.0, 70.0, 92, 1640995260000L),

            // Invalid messages (should fail some rules)
            createSensorReading(avroSchema, null, 25.0, 60.0, 78, 1640995320000L), // sensorId null
            createSensorReading(avroSchema, "TEMP003", 75.0, 55.0, 65, 1640995380000L) // temperature > 50
        )
    }

    private fun createSensorReading(
        avroSchema: Schema,
        sensorId: String?,
        temperature: Double,
        humidity: Double,
        batteryLevel: Int,
        timestamp: Long
    ): GenericRecord {
        val record = GenericData.Record(avroSchema)
        record.put("sensorId", sensorId)
        record.put("temperature", temperature)
        record.put("humidity", humidity)
        record.put("batteryLevel", batteryLevel)
        record.put("timestamp", timestamp)
        return record
    }

    /**
     * Produces AVRO user event messages to the specified topic.
     */
    fun produceUserEventMessages(topicName: String, avroSchema: Schema) {
        val messages = createUserEventMessages(avroSchema)

        messages.forEachIndexed { index, record ->
            val producerRecord = ProducerRecord(topicName, "user-key-$index", record)
            try {
                val metadata = producer.send(producerRecord).get()
                logger.info("Produced AVRO user event to topic: ${metadata.topic()}, partition: ${metadata.partition()}, offset: ${metadata.offset()}")
            } catch (e: Exception) {
                logger.error("Failed to produce AVRO user event", e)
                throw e
            }
        }
        producer.flush()
    }

    private fun createUserEventMessages(avroSchema: Schema): List<GenericRecord> {
        return listOf(
            // Valid messages
            createUserEvent(avroSchema, "evt001", "user1", "LOGIN", 1640995200000L, 18628, 3600, 85.5),
            createUserEvent(avroSchema, "evt002", "user2", "LOGOUT", 1640995260000L, 18628, 1800, 92.0),

            // Invalid messages
            createUserEvent(avroSchema, "evt003", null, "LOGIN", 1640995320000L, 18628, 2400, 75.0), // userId null
            createUserEvent(avroSchema, "evt004", "user3", "SIGNUP", 1640995380000L, 18628, null, 150.0) // score > 100
        )
    }

    private fun createUserEvent(
        avroSchema: Schema,
        eventId: String,
        userId: String?,
        eventType: String,
        timestamp: Long,
        eventDate: Int,
        sessionDuration: Int?,
        score: Double
    ): GenericRecord {
        val record = GenericData.Record(avroSchema)
        record.put("eventId", eventId)
        record.put("userId", userId)
        record.put("eventType", eventType)
        record.put("timestamp", timestamp)
        record.put("eventDate", eventDate)
        record.put("sessionDuration", sessionDuration)
        record.put("score", score)

        // Create metadata record
        val metadataSchema = avroSchema.getField("metadata").schema()
        val metadata = GenericData.Record(metadataSchema)
        metadata.put("ipAddress", "***********")
        metadata.put("userAgent", "Mozilla/5.0")
        metadata.put("country", "US")
        metadata.put("deviceType", "DESKTOP")
        record.put("metadata", metadata)

        // Set default values for arrays and maps
        record.put("tags", listOf("test", "integration"))
        record.put("properties", mapOf("source" to "test", "version" to "1.0"))

        return record
    }

    fun close() {
        producer.close()
    }
}
