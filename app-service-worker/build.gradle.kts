import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("org.springframework.boot") version Versions.springBoot
    id("io.spring.dependency-management") version Versions.springBootDependencyManagement
    kotlin("plugin.spring") version "1.9.21"
    id("com.spars.dens.kotlin-common-conventions")
}

group = "com.spars.dens"
version = "unspecified"

java {
    sourceCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
    maven {
        url = uri("https://packages.confluent.io/maven/")
    }
    maven {
        url = uri("https://jitpack.io")
    }
}

dependencies {
    implementation(project(":app-service-folder"))
    implementation(project(":app-core-utils"))
    implementation(project(":app-core-datamodel"))

    implementation("org.springframework.boot:spring-boot-starter-aop")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("org.springframework.boot:spring-boot-starter-amqp")
    implementation("org.springframework.boot:spring-boot-starter-mail")
    implementation("org.springframework:spring-tx")
    implementation("org.springframework.retry:spring-retry")
    // Commons- CSV
    implementation("org.apache.commons:commons-csv:1.12.0")
    // Commons - VFS
    implementation("org.apache.commons:commons-vfs2:2.9.0")
    implementation("com.github.mwiede:jsch:0.2.8")
    implementation("commons-net:commons-net:3.11.1")

    // PostgreSQL jdbc driver
    runtimeOnly("org.postgresql:postgresql:42.7.4")
    // Oracle jdbc driver
    runtimeOnly("com.oracle.database.*************************")
    // Kafka
    implementation("org.apache.kafka:kafka-clients:4.0.0")
    implementation("io.confluent:kafka-schema-registry-client:7.8.2")
    implementation("io.confluent:kafka-json-schema-provider:7.8.2")
    implementation("io.confluent:kafka-protobuf-provider:7.8.2")
    implementation("io.confluent:kafka-avro-serializer:7.8.2")
    implementation("io.confluent:kafka-protobuf-serializer:7.8.2")

    implementation("com.fasterxml.jackson.module:jackson-module-kotlin:2.18.4")

    // Add Redisson
    implementation("org.redisson:redisson-spring-boot-starter:3.27.1")

    // Add Spring test dependencies
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework:spring-test")

    // Test dependencies for AVRO and PROTOBUF
    testImplementation("org.apache.avro:avro:1.12.0")
    testImplementation("com.google.protobuf:protobuf-java:3.25.5")
    testImplementation("com.google.protobuf:protobuf-java-util:3.25.5")
    testImplementation("com.google.protobuf:protoc:3.25.5")

    // JSON Schema inference library
    implementation("com.github.saasquatch:json-schema-inferrer:0.2.1")
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs += "-Xjsr305=strict"
        jvmTarget = "21"
    }
}

tasks.withType<Test> {
    useJUnitPlatform()
}

// Disable bootJar since this is a library module
tasks.getByName("bootJar") {
    enabled = false
}

// Enable plain jar for library
tasks.getByName("jar") {
    enabled = true
}
