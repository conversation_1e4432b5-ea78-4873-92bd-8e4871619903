plugins {
    id("org.springframework.boot") version Versions.springBoot
    id("io.spring.dependency-management") version Versions.springBootDependencyManagement
    kotlin("plugin.spring") version "1.9.21"
    id("com.spars.dens.kotlin-common-conventions")
}

group = "com.spars.dens"
version = "unspecified"

java {
    sourceCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
}

dependencies {
    dependencies {
        implementation(project(":app-core-utils"))
        implementation(project(":app-core-datamodel"))

        implementation("org.apache.commons:commons-vfs2:2.9.0")
        implementation("org.springframework:spring-context")

        testImplementation(kotlin("test"))
    }
}

tasks.test {
    useJUnitPlatform()
}

// Disable bootJar since this is a library module
tasks.getByName("bootJar") {
    enabled = false
}

// Enable plain jar for library
tasks.getByName("jar") {
    enabled = true
}

kotlin {
    jvmToolchain(21)
}
