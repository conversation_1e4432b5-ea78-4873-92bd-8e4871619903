package com.spars.dens.folder.services

import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import org.apache.commons.vfs2.FileObject
import org.apache.commons.vfs2.FileSystemOptions
import org.apache.commons.vfs2.auth.StaticUserAuthenticator
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder
import org.apache.commons.vfs2.impl.StandardFileSystemManager
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder
import org.springframework.stereotype.Service

@Service
class FileService {

    /**
     * Retrieves a file from the specified path using the provided connection settings.
     * * @param connectionSettings The folder connection settings containing authentication details
     * @param filePath The full path to the file
     * @return The FileObject representing the requested file
     */
    fun getFileFromPath(
        connectionSettings: ConnectionSettings.FolderConnectionSettings,
        filePath: String
    ): FileObject {
        val authenticator = StaticUserAuthenticator(null, connectionSettings.userName, connectionSettings.password)
        val fsOptions = FileSystemOptions()

        // Configure FTP settings
        FtpFileSystemConfigBuilder.getInstance().setPassiveMode(fsOptions, true)

        // Configure SFTP settings
        SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(fsOptions, "no")

        // Set authenticator
        DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(fsOptions, authenticator)

        // Initialize file system manager
        val fsManager = StandardFileSystemManager()
        fsManager.init()
        // Construct URI from components
        val baseUri = connectionSettings.getBaseFolderURIString()
        val cleanPath = filePath.removePrefix("/")
        val fileUri = "$baseUri/$cleanPath"
        // Resolve and return the file
        return fsManager.resolveFile(fileUri, fsOptions)
    }
}
