package com.spars.dens.folder.services

import com.spars.dens.core.datamodel.datasource.models.ConnectionSettings
import com.spars.dens.core.datamodel.datasource.models.DataSource
import com.spars.dens.core.datamodel.tenant.models.TenantId
import com.spars.dens.core.utils.logging.MonitoredFunction
import com.spars.dens.core.utils.logging.MonitoredParameter
import org.apache.commons.vfs2.FileObject
import org.apache.commons.vfs2.FileSystemOptions
import org.apache.commons.vfs2.FileType
import org.apache.commons.vfs2.auth.StaticUserAuthenticator
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder
import org.apache.commons.vfs2.impl.StandardFileSystemManager
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder
import org.springframework.stereotype.Service
import java.util.function.Predicate

@Service
class FolderService {

    @MonitoredFunction("Folder service to health check for folder data source")
    fun folderDataSourceExists(
        @MonitoredParameter settings: ConnectionSettings.FolderConnectionSettings
    ): Boolean {
        val userDefaultHomeDirectory = getDirectoryInFileServer(settings)
        return userDefaultHomeDirectory.exists() && userDefaultHomeDirectory.type.hasChildren()
    }

    private fun getDirectoryInFileServer(
        settings: ConnectionSettings.FolderConnectionSettings,
        folderRelativePath: String ? = null
    ): FileObject {
        val authenticator = StaticUserAuthenticator(null, settings.userName, settings.password)
        val fsOptions = FileSystemOptions() // Correct way to initialize options
        FtpFileSystemConfigBuilder.getInstance().setPassiveMode(fsOptions, true)
        SftpFileSystemConfigBuilder.getInstance().setStrictHostKeyChecking(fsOptions, "no")
        DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(fsOptions, authenticator)
        val fsManager = StandardFileSystemManager()
        fsManager.init()
        val directoryURI = if (folderRelativePath.isNullOrBlank()) {
            settings.getBaseFolderURIString()
        } else {
            settings.getBaseFolderURIString().removeSuffix("/") + "/" + folderRelativePath.removePrefix("/")
        }
        return fsManager.resolveFile(directoryURI, fsOptions)
    }

    fun filterFiles(
        tenantId: TenantId,
        dataSource: DataSource,
        folderRelativePath: String,
        fileNameFilters: List<Predicate<String>>,
        limit: Int? = null
    ): List<FileObject> {
        val folderConnectionSettings = dataSource.connectionSettings as ConnectionSettings.FolderConnectionSettings

        val baseFolder = getDirectoryInFileServer(folderConnectionSettings, folderRelativePath)
        // Ensure the base folder exists and is a directory
        if (!baseFolder.exists() || !baseFolder.type.hasChildren()) {
            throw IllegalArgumentException("Base folder does not exist or is not a directory: $folderRelativePath")
        }

        val matchedFiles = mutableListOf<FileObject>()
        // Recursive function to traverse directories
        fun collectFiles(folder: FileObject) {
            for (child in folder.children) {
                if (child.type.hasChildren()) {
                    // If it's a directory, recurse
                    collectFiles(child)
                } else if (child.type == FileType.FILE) {
                    // Apply filters to file names
                    val fileName = child.name.baseName
                    if (fileNameFilters.all { it.test(fileName) }) {
                        matchedFiles.add(child)
                        if (limit != null && matchedFiles.size >= limit) {
                            return
                        }
                    }
                }
            }
        }
        // Start the recursion from the base folder
        collectFiles(baseFolder)
        return matchedFiles
    }
}
