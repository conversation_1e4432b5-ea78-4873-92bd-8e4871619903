package com.spars.dens.core.datamodel.asset.models

import com.spars.dens.core.datamodel.dataformat.models.DataFormat
import com.spars.dens.core.datamodel.dataformat.models.DataFormatSpecificSettings
import com.spars.dens.core.datamodel.tenant.models.TenantId
import com.spars.dens.datamodel.jooq.tables.records.AssetSchemaVersionRecord
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaInstant
import kotlinx.datetime.toKotlinInstant
import kotlinx.serialization.encodeToString
import org.jooq.JSONB
import java.time.ZoneOffset

data class AssetSchemaVersion(
    val assetSchemaVersionId: AssetSchemaVersionId,
    val tenantId: TenantId,
    val assetId: AssetId,
    val schemaVersion: Int,
    val dataFormat: DataFormat,
    val dataFormatSpecificSettings: DataFormatSpecificSettings?,
    val schema: Schema,
    val createdAt: Instant,
    val updatedAt: Instant?,
    val isDeleted: Boolean
) {

    companion object {
        fun from(
            record: AssetSchemaVersionRecord
        ): AssetSchemaVersion {
            return AssetSchemaVersion(
                assetSchemaVersionId = AssetSchemaVersionId.from(record.assetSchemaVersionId!!),
                tenantId = TenantId.from(record.tenantId!!),
                assetId = AssetId.from(record.assetId!!),
                schemaVersion = record.schemaVersion!!,
                dataFormat = DataFormat.valueOf(record.dataFormat!!),
                dataFormatSpecificSettings = record.dataFormatSpecificSettings?.let { DataFormatSpecificSettings.JSON.decodeFromString(it.data()) },
                schema = Schema.JSON.decodeFromString(record.schema!!.data()),
                createdAt = record.createdAt!!.toInstant().toKotlinInstant(),
                updatedAt = record.updatedAt?.toInstant()?.toKotlinInstant(),
                isDeleted = record.isDeleted!!
            )
        }
    }

    data class CreateData(
        val assetSchemaVersionId: AssetSchemaVersionId? = null,
        val tenantId: TenantId,
        val assetId: AssetId,
        val schemaVersion: Int,
        val dataFormat: DataFormat,
        val dataFormatSpecificSettings: DataFormatSpecificSettings?,
        val schema: Schema,
        val createdAt: Instant = Clock.System.now(),
        val updatedAt: Instant? = null,
        val isDeleted: Boolean = false
    ) {
        fun toJooqRecord(): AssetSchemaVersionRecord {
            return AssetSchemaVersionRecord(
                assetSchemaVersionId = assetSchemaVersionId?.value,
                tenantId = tenantId.value,
                assetId = assetId.value,
                schemaVersion = schemaVersion,
                dataFormat = dataFormat.name,
                dataFormatSpecificSettings = dataFormatSpecificSettings?.let { JSONB.valueOf(DataFormatSpecificSettings.JSON.encodeToString(it)) },
                schema = JSONB.valueOf(Schema.JSON.encodeToString(schema)),
                createdAt = createdAt.toJavaInstant().atOffset(ZoneOffset.UTC),
                updatedAt = updatedAt?.toJavaInstant()?.atOffset(ZoneOffset.UTC),
                isDeleted = isDeleted
            )
        }
    }

    data class CreateDataForNewAsset(
        val assetSchemaVersionId: AssetSchemaVersionId? = null,
        val tenantId: TenantId,
        val schemaVersion: Int,
        val dataFormat: DataFormat,
        val dataFormatSpecificSettings: DataFormatSpecificSettings?,
        val schema: Schema,
        val createdAt: Instant = Clock.System.now(),
        val updatedAt: Instant? = null,
        val isDeleted: Boolean = false
    ) {
        fun toJooqRecord(assetId: AssetId,): AssetSchemaVersionRecord {
            return AssetSchemaVersionRecord(
                assetSchemaVersionId = assetSchemaVersionId?.value,
                tenantId = tenantId.value,
                assetId = assetId.value,
                schemaVersion = schemaVersion,
                dataFormat = dataFormat.name,
                dataFormatSpecificSettings = dataFormatSpecificSettings?.let { JSONB.valueOf(DataFormatSpecificSettings.JSON.encodeToString(it)) },
                schema = JSONB.valueOf(Schema.JSON.encodeToString(schema)),
                createdAt = createdAt.toJavaInstant().atOffset(ZoneOffset.UTC),
                updatedAt = updatedAt?.toJavaInstant()?.atOffset(ZoneOffset.UTC),
                isDeleted = isDeleted
            )
        }

        fun toCreateData(assetId: AssetId): CreateData {
            return CreateData(
                assetSchemaVersionId = assetSchemaVersionId,
                tenantId = tenantId,
                assetId = assetId,
                schemaVersion = schemaVersion,
                dataFormat = dataFormat,
                dataFormatSpecificSettings = dataFormatSpecificSettings,
                schema = schema,
                createdAt = createdAt,
                updatedAt = updatedAt,
                isDeleted = isDeleted
            )
        }
    }

    fun toJooqRecord(): AssetSchemaVersionRecord {
        return AssetSchemaVersionRecord(
            assetSchemaVersionId = assetSchemaVersionId.value,
            tenantId = tenantId.value,
            assetId = assetId.value,
            schemaVersion = schemaVersion,
            dataFormat = dataFormat.name,
            dataFormatSpecificSettings = dataFormatSpecificSettings?.let { JSONB.valueOf(DataFormatSpecificSettings.JSON.encodeToString(it)) },
            schema = JSONB.valueOf(Schema.JSON.encodeToString(schema)),
            createdAt = createdAt.toJavaInstant().atOffset(ZoneOffset.UTC),
            updatedAt = updatedAt?.toJavaInstant()?.atOffset(ZoneOffset.UTC),
            isDeleted = isDeleted
        )
    }
}
