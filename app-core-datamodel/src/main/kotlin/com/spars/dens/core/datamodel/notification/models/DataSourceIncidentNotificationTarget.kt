package com.spars.dens.core.datamodel.notification.models

import com.spars.dens.core.datamodel.datasource.models.DataSourceId
import com.spars.dens.core.datamodel.tenant.models.TenantId
import com.spars.dens.datamodel.jooq.tables.records.DataSourceIncidentNotificationTargetRecord
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.toJavaInstant
import kotlinx.datetime.toKotlinInstant
import kotlinx.serialization.encodeToString
import org.jooq.JSONB
import java.time.ZoneOffset

data class DataSourceIncidentNotificationTarget(
    val dataSourceIncidentNotificationTargetId: DataSourceIncidentNotificationTargetId,
    val tenantId: TenantId,
    val dataSourceId: DataSourceId,
    val notificationType: NotificationType,
    val targetAddress: String,
    val createdAt: Instant,
    val updatedAt: Instant?,
    val isDeleted: Boolean
) {
    companion object {
        fun from(record: DataSourceIncidentNotificationTargetRecord): DataSourceIncidentNotificationTarget {
            return DataSourceIncidentNotificationTarget(
                dataSourceIncidentNotificationTargetId = DataSourceIncidentNotificationTargetId.from(record.dataSourceIncidentNotificationTargetId!!),
                tenantId = TenantId.from(record.tenantId!!),
                dataSourceId = DataSourceId.from(record.dataSourceId!!),
                notificationType = NotificationType.valueOf(record.notificationType!!),
                targetAddress = record.targetAddress!!,
                createdAt = record.createdAt!!.toInstant().toKotlinInstant(),
                updatedAt = record.updatedAt?.toInstant()?.toKotlinInstant(),
                isDeleted = record.isDeleted!!
            )
        }
    }

    data class CreateData(
        val dataSourceIncidentNotificationTargetId: DataSourceIncidentNotificationTargetId? = null,
        val tenantId: TenantId,
        val dataSourceId: DataSourceId,
        val notificationType: NotificationType,
        val targetAddress: String,
        val credentials: WebhookCredentials? = null,
        val createdAt: Instant = Clock.System.now(),
        val updatedAt: Instant? = null,
        val isDeleted: Boolean = false
    ) {
        fun toJooqRecord(): DataSourceIncidentNotificationTargetRecord {
            return DataSourceIncidentNotificationTargetRecord(
                dataSourceIncidentNotificationTargetId = dataSourceIncidentNotificationTargetId?.value,
                tenantId = tenantId.value,
                dataSourceId = dataSourceId.value,
                notificationType = notificationType.name,
                targetAddress = targetAddress,
                createdAt = createdAt.toJavaInstant().atOffset(ZoneOffset.UTC),
                updatedAt = updatedAt?.toJavaInstant()?.atOffset(ZoneOffset.UTC),
                isDeleted = isDeleted
            )
        }
    }

    fun toJooqRecord(): DataSourceIncidentNotificationTargetRecord {
        return DataSourceIncidentNotificationTargetRecord(
            dataSourceIncidentNotificationTargetId = dataSourceIncidentNotificationTargetId.value,
            tenantId = tenantId.value,
            dataSourceId = dataSourceId.value,
            notificationType = notificationType.name,
            targetAddress = targetAddress,
            createdAt = createdAt.toJavaInstant().atOffset(ZoneOffset.UTC),
            updatedAt = updatedAt?.toJavaInstant()?.atOffset(ZoneOffset.UTC),
            isDeleted = isDeleted
        )
    }

    fun markAsDeleted() = copy(isDeleted = true)
}
