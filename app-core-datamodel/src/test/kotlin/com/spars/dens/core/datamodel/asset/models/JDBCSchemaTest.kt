package com.spars.dens.core.datamodel.asset.models

import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import java.sql.Types

class JDBCSchemaTest {

    @Test
    fun `test isEquivalentTo with identical schemas`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                J<PERSON><PERSON><PERSON><PERSON>umn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        assertTrue(schema1.isEquivalentTo(schema2))
        assertTrue(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with different column order`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL"),
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL")
            )
        )

        assertTrue(schema1.isEquivalentTo(schema2))
        assertTrue(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with different column types`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "BIGINT", false, 10, null, null, Types.BIGINT, "PostgreSQL"), // Different type
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        assertFalse(schema1.isEquivalentTo(schema2))
        assertFalse(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with different column nullability`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", false, 255, null, null, Types.VARCHAR, "PostgreSQL"), // Different nullability
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        assertFalse(schema1.isEquivalentTo(schema2))
        assertFalse(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with different column size`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 100, null, null, Types.VARCHAR, "PostgreSQL"), // Different size
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        assertFalse(schema1.isEquivalentTo(schema2))
        assertFalse(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with different column count`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL")
                // Missing created_at column
            )
        )

        assertFalse(schema1.isEquivalentTo(schema2))
        assertFalse(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with different column names`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("full_name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"), // Different name
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        assertFalse(schema1.isEquivalentTo(schema2))
        assertFalse(schema2.isEquivalentTo(schema1))
    }

    @Test
    fun `test isEquivalentTo with case-insensitive column names`() {
        val schema1 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("ID", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("NAME", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("CREATED_AT", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        val schema2 = Schema.JDBCSchema(
            columns = listOf(
                JDBCColumn("id", "INTEGER", false, 10, null, null, Types.INTEGER, "PostgreSQL"),
                JDBCColumn("name", "VARCHAR", true, 255, null, null, Types.VARCHAR, "PostgreSQL"),
                JDBCColumn("created_at", "TIMESTAMP", true, null, null, null, Types.TIMESTAMP, "PostgreSQL")
            )
        )

        assertTrue(schema1.isEquivalentTo(schema2))
        assertTrue(schema2.isEquivalentTo(schema1))
    }
}
