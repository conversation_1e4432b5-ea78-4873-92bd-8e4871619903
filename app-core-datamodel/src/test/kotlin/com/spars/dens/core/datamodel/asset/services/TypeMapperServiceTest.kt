package com.spars.dens.core.datamodel.asset.services

import com.fasterxml.jackson.databind.ObjectMapper
import com.spars.dens.core.datamodel.asset.models.FieldType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import java.sql.Types

class TypeMapperServiceTest {

    private val typeMapperService = TypeMapperService()
    private val objectMapper = ObjectMapper()

    @Test
    fun `mapJdbcTypeToFieldType should map boolean types correctly`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapJdbcTypeToFieldType(Types.BIT))
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapJdbcTypeToFieldType(Types.BOOLEAN))
    }

    @Test
    fun `mapJdbcTypeToFieldType should map integer types correctly`() {
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.TINYINT))
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.SMALLINT))
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.INTEGER))
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.BIGINT))
    }

    @Test
    fun `mapJdbcTypeToFieldType should map decimal types correctly based on scale`() {
        // Without scale or scale = 0 should be NUMBER
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.NUMERIC))
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.NUMERIC, 0))
        assertEquals(FieldType.NUMBER, typeMapperService.mapJdbcTypeToFieldType(Types.DECIMAL, 0))

        // With scale > 0 should be DECIMAL
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJdbcTypeToFieldType(Types.NUMERIC, 2))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJdbcTypeToFieldType(Types.DECIMAL, 3))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJdbcTypeToFieldType(Types.FLOAT))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJdbcTypeToFieldType(Types.REAL))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJdbcTypeToFieldType(Types.DOUBLE))
    }

    @Test
    fun `mapJdbcTypeToFieldType should map text types correctly`() {
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.CHAR))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.VARCHAR))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.LONGVARCHAR))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.NCHAR))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.NVARCHAR))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.LONGNVARCHAR))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.CLOB))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(Types.NCLOB))
    }

    @Test
    fun `mapJdbcTypeToFieldType should map date and time types correctly`() {
        assertEquals(FieldType.DATE, typeMapperService.mapJdbcTypeToFieldType(Types.DATE))
        assertEquals(FieldType.TIME, typeMapperService.mapJdbcTypeToFieldType(Types.TIME))
        assertEquals(FieldType.TIME, typeMapperService.mapJdbcTypeToFieldType(Types.TIME_WITH_TIMEZONE))
        assertEquals(FieldType.DATE_TIME, typeMapperService.mapJdbcTypeToFieldType(Types.TIMESTAMP))
        assertEquals(FieldType.TIMESTAMP, typeMapperService.mapJdbcTypeToFieldType(Types.TIMESTAMP_WITH_TIMEZONE))
    }

    @Test
    fun `mapJdbcTypeToFieldType should map object and array types correctly`() {
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.BINARY))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.VARBINARY))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.LONGVARBINARY))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.BLOB))
        assertEquals(FieldType.ARRAY, typeMapperService.mapJdbcTypeToFieldType(Types.ARRAY))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.STRUCT))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.JAVA_OBJECT))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJdbcTypeToFieldType(Types.OTHER))
    }

    @Test
    fun `mapJdbcTypeToFieldType should default to TEXT for unknown types`() {
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(null))
        assertEquals(FieldType.TEXT, typeMapperService.mapJdbcTypeToFieldType(9999)) // Non-existent type
    }

    @Test
    fun `mapAvroTypeToFieldType should map primitive types correctly`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapAvroTypeToFieldType("boolean"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapAvroTypeToFieldType("int"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapAvroTypeToFieldType("long"))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapAvroTypeToFieldType("float"))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapAvroTypeToFieldType("double"))
        assertEquals(FieldType.TEXT, typeMapperService.mapAvroTypeToFieldType("string"))
        assertEquals(FieldType.TEXT, typeMapperService.mapAvroTypeToFieldType("bytes"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapAvroTypeToFieldType("record"))
        assertEquals(FieldType.TEXT, typeMapperService.mapAvroTypeToFieldType("enum"))
        assertEquals(FieldType.ARRAY, typeMapperService.mapAvroTypeToFieldType("array"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapAvroTypeToFieldType("map"))
        assertEquals(FieldType.TEXT, typeMapperService.mapAvroTypeToFieldType("union"))
        assertEquals(FieldType.TEXT, typeMapperService.mapAvroTypeToFieldType("fixed"))
    }

    @Test
    fun `mapAvroTypeToFieldType should handle logical types correctly`() {
        assertEquals(FieldType.DATE, typeMapperService.mapAvroTypeToFieldType("int", "date"))
        assertEquals(FieldType.TIME, typeMapperService.mapAvroTypeToFieldType("int", "time-millis"))
        assertEquals(FieldType.TIME, typeMapperService.mapAvroTypeToFieldType("long", "time-micros"))
        assertEquals(FieldType.TIMESTAMP, typeMapperService.mapAvroTypeToFieldType("long", "timestamp-millis"))
        assertEquals(FieldType.TIMESTAMP, typeMapperService.mapAvroTypeToFieldType("long", "timestamp-micros"))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapAvroTypeToFieldType("bytes", "decimal"))
        assertEquals(FieldType.TEXT, typeMapperService.mapAvroTypeToFieldType("string", "uuid"))
    }

    @Test
    fun `mapAvroTypeToFieldType should be case insensitive`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapAvroTypeToFieldType("BOOLEAN"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapAvroTypeToFieldType("INT"))
        assertEquals(FieldType.DATE, typeMapperService.mapAvroTypeToFieldType("int", "DATE"))
    }

    @Test
    fun `mapProtobufTypeToFieldType should map basic types correctly`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapProtobufTypeToFieldType("bool"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("int32"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("int64"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("uint32"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("uint64"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("sint32"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("sint64"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("fixed32"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("fixed64"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("sfixed32"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("sfixed64"))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapProtobufTypeToFieldType("float"))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapProtobufTypeToFieldType("double"))
        assertEquals(FieldType.TEXT, typeMapperService.mapProtobufTypeToFieldType("string"))
        assertEquals(FieldType.TEXT, typeMapperService.mapProtobufTypeToFieldType("bytes"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapProtobufTypeToFieldType("message"))
        assertEquals(FieldType.TEXT, typeMapperService.mapProtobufTypeToFieldType("enum"))
        assertEquals(FieldType.ARRAY, typeMapperService.mapProtobufTypeToFieldType("repeated"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapProtobufTypeToFieldType("map"))
    }

    @Test
    fun `mapProtobufTypeToFieldType should handle well-known types correctly`() {
        assertEquals(FieldType.TIMESTAMP, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.timestamp"))
        assertEquals(FieldType.TEXT, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.duration"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.any"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.struct"))
        assertEquals(FieldType.TEXT, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.value"))
        assertEquals(FieldType.ARRAY, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.listvalue"))
        assertEquals(FieldType.TEXT, typeMapperService.mapProtobufTypeToFieldType("google.protobuf.nullvalue"))
    }

    @Test
    fun `mapProtobufTypeToFieldType should be case insensitive`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapProtobufTypeToFieldType("BOOL"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapProtobufTypeToFieldType("INT32"))
        assertEquals(FieldType.TIMESTAMP, typeMapperService.mapProtobufTypeToFieldType("GOOGLE.PROTOBUF.TIMESTAMP"))
    }

    @Test
    fun `mapJsonSchemaTypeToFieldType should map basic types correctly`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapJsonSchemaTypeToFieldType("boolean"))
        assertEquals(FieldType.NUMBER, typeMapperService.mapJsonSchemaTypeToFieldType("integer"))
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJsonSchemaTypeToFieldType("number"))
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaTypeToFieldType("string"))
        assertEquals(FieldType.OBJECT, typeMapperService.mapJsonSchemaTypeToFieldType("object"))
        assertEquals(FieldType.ARRAY, typeMapperService.mapJsonSchemaTypeToFieldType("array"))
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaTypeToFieldType("null"))
    }

    @Test
    fun `mapJsonSchemaTypeToFieldType should handle string formats correctly`() {
        assertEquals(FieldType.DATE, typeMapperService.mapJsonSchemaTypeToFieldType("string", "date"))
        assertEquals(FieldType.TIME, typeMapperService.mapJsonSchemaTypeToFieldType("string", "time"))
        assertEquals(FieldType.DATE_TIME, typeMapperService.mapJsonSchemaTypeToFieldType("string", "date-time"))
        assertEquals(FieldType.DATE_TIME, typeMapperService.mapJsonSchemaTypeToFieldType("string", "datetime"))
        assertEquals(FieldType.TIMESTAMP, typeMapperService.mapJsonSchemaTypeToFieldType("string", "timestamp"))
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaTypeToFieldType("string", "email"))
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaTypeToFieldType("string", "uri"))
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaTypeToFieldType("string", "uuid"))
    }

    @Test
    fun `mapJsonSchemaTypeToFieldType should be case insensitive`() {
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapJsonSchemaTypeToFieldType("BOOLEAN"))
        assertEquals(FieldType.DATE, typeMapperService.mapJsonSchemaTypeToFieldType("STRING", "DATE"))
    }

    @Test
    fun `mapJsonSchemaNodeToFieldType should handle simple type nodes`() {
        val booleanNode = objectMapper.readTree("""{"type": "boolean"}""")
        assertEquals(FieldType.BOOLEAN, typeMapperService.mapJsonSchemaNodeToFieldType(booleanNode))

        val stringNode = objectMapper.readTree("""{"type": "string"}""")
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaNodeToFieldType(stringNode))

        val numberNode = objectMapper.readTree("""{"type": "number"}""")
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJsonSchemaNodeToFieldType(numberNode))
    }

    @Test
    fun `mapJsonSchemaNodeToFieldType should handle format in nodes`() {
        val dateNode = objectMapper.readTree("""{"type": "string", "format": "date"}""")
        assertEquals(FieldType.DATE, typeMapperService.mapJsonSchemaNodeToFieldType(dateNode))

        val dateTimeNode = objectMapper.readTree("""{"type": "string", "format": "date-time"}""")
        assertEquals(FieldType.DATE_TIME, typeMapperService.mapJsonSchemaNodeToFieldType(dateTimeNode))
    }

    @Test
    fun `mapJsonSchemaNodeToFieldType should handle array of types`() {
        val unionNode = objectMapper.readTree("""{"type": ["string", "null"]}""")
        assertEquals(FieldType.TEXT, typeMapperService.mapJsonSchemaNodeToFieldType(unionNode))

        val numberUnionNode = objectMapper.readTree("""{"type": ["number", "null"]}""")
        assertEquals(FieldType.DECIMAL, typeMapperService.mapJsonSchemaNodeToFieldType(numberUnionNode))
    }

    @Test
    fun `mapFieldTypeToJsonSchemaType should map FieldType to JSON Schema types correctly`() {
        assertEquals("string", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.TEXT))
        assertEquals("integer", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.NUMBER))
        assertEquals("number", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.DECIMAL))
        assertEquals("boolean", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.BOOLEAN))
        assertEquals("string", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.DATE))
        assertEquals("string", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.TIME))
        assertEquals("string", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.DATE_TIME))
        assertEquals("string", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.TIMESTAMP))
        assertEquals("object", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.OBJECT))
        assertEquals("array", typeMapperService.mapFieldTypeToJsonSchemaType(FieldType.ARRAY))
    }

    @Test
    fun `getJsonSchemaFormatForFieldType should return correct formats for date-time types`() {
        assertEquals("date", typeMapperService.getJsonSchemaFormatForFieldType(FieldType.DATE))
        assertEquals("time", typeMapperService.getJsonSchemaFormatForFieldType(FieldType.TIME))
        assertEquals("date-time", typeMapperService.getJsonSchemaFormatForFieldType(FieldType.DATE_TIME))
        assertEquals("date-time", typeMapperService.getJsonSchemaFormatForFieldType(FieldType.TIMESTAMP))
        assertEquals(null, typeMapperService.getJsonSchemaFormatForFieldType(FieldType.TEXT))
        assertEquals(null, typeMapperService.getJsonSchemaFormatForFieldType(FieldType.NUMBER))
        assertEquals(null, typeMapperService.getJsonSchemaFormatForFieldType(FieldType.BOOLEAN))
        assertEquals(null, typeMapperService.getJsonSchemaFormatForFieldType(FieldType.OBJECT))
        assertEquals(null, typeMapperService.getJsonSchemaFormatForFieldType(FieldType.ARRAY))
    }
}
