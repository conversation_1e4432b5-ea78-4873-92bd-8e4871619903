plugins {
    id("org.springframework.boot") version Versions.springBoot
    id("io.spring.dependency-management") version Versions.springBootDependencyManagement
    kotlin("plugin.spring") version "1.9.21"
    id("org.flywaydb.flyway") version Versions.flyway
    id("nu.studer.jooq") version Versions.jooqPlugin
    id("com.spars.dens.kotlin-common-conventions")
}

group = "com.spars.dens"
version = "unspecified"

repositories {
    mavenCentral()
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
}

dependencies {
    implementation(kotlin("stdlib"))
    implementation("org.springframework.boot:spring-boot-starter-jooq") {
        exclude(group = "org.jooq", module = "jooq")
    }
    implementation(project(":app-core-utils"))
    implementation("org.apache.kafka:kafka-clients:4.0.0")

    implementation("org.jooq:jooq:${Versions.jooq}")
    implementation("org.flywaydb:flyway-core:${Versions.flyway}")
    jooqGenerator("org.postgresql:postgresql:${Versions.postgresql}")
    implementation("org.springframework.boot:spring-boot-starter-aop")
}

sourceSets {
    main {
        java.srcDirs("src/main/kotlin", "build/generated-src/jooq/main")
    }
}

flyway {
    url = DBConfig.getDbUrl()
    user = DBConfig.dbUser
    password = DBConfig.dbPassword
    schemas = arrayOf(DBConfig.dbSchema)
    placeholders = mapOf()
    validateMigrationNaming = true
    cleanDisabled = false
}

tasks.getByName<Test>("test") {
    useJUnitPlatform()
}

// Disable bootJar since this is a library module
tasks.getByName("bootJar") {
    enabled = false
}

// Enable plain jar for library
tasks.getByName("jar") {
    enabled = true
}

buildscript {
    configurations["classpath"].resolutionStrategy.eachDependency {
        if (requested.group.startsWith("org.jooq") && requested.name.startsWith("jooq")) {
            useVersion(Versions.jooq)
        }
    }
}

jooq {
    version.set(Versions.jooq)
    edition.set(nu.studer.gradle.jooq.JooqEdition.OSS)

    val jooqGenerationExclusions = listOf(
        // Flyway tables
        "flyway_.*"
    )

    configurations {
        create("main") {
            generateSchemaSourceOnCompilation.set(true)
            jooqConfiguration.apply {
                logging = org.jooq.meta.jaxb.Logging.WARN

                jdbc.apply {
                    driver = "org.postgresql.Driver"
                    url = DBConfig.getDbUrl()
                    user = DBConfig.dbUser
                    password = DBConfig.dbPassword
                }

                generator.apply {
                    name = "org.jooq.codegen.KotlinGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = DBConfig.dbSchema
                        includes = ".*"
                        excludes = jooqGenerationExclusions.joinToString(separator = "|")
                        isIncludeExcludePackageRoutines = true
                        forcedTypes = listOf(
                            org.jooq.meta.jaxb.ForcedType()
                                .withUserType("org.jooq.JSONB")
                                .withConverter("com.spars.dens.core.datamodel.jooq.JsonbEncryptionConverter")
                                .withIncludeExpression("(CONNECTION_SETTINGS)")
                                .withIncludeTypes("(JSONB)"),
                        )
                    }

                    generate.apply {
                        isDeprecated = false
                        isRecords = true
                        isImmutablePojos = true
                        isFluentSetters = true
                    }

                    target.apply {
                        packageName = "com.spars.dens.datamodel.jooq"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

tasks {
    val flywayMigrateTask = named<org.flywaydb.gradle.task.FlywayMigrateTask>("flywayMigrate")
    named<nu.studer.gradle.jooq.JooqGenerate>("generateJooq") {
        // ensure database schema has been prepared by Flyway before generating the jOOQ sources
        dependsOn(flywayMigrateTask)

        // declare Flyway migration scripts as inputs on the jOOQ task
        inputs.files(fileTree("src/main/resources/db_migration"))
            .withPropertyName("migrations")
            .withPathSensitivity(PathSensitivity.RELATIVE)

        // make jOOQ task participate in incremental builds (and build caching)
        allInputsDeclared.set(true)

        // Set locale to English to avoid Turkish character issues
        doFirst {
            System.setProperty("user.language", "en")
            System.setProperty("user.country", "US")
        }
    }

    compileKotlin {
        dependsOn("generateJooq")
    }
}
