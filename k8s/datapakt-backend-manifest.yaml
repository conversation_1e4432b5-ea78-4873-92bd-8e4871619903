---
# ConfigMap for non-sensitive configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: datapakt-backend-config
  namespace: default
data:
  # Spring Configuration
  SPRING_PROFILES_ACTIVE: "staging"

  # JVM Configuration
  JVM_XMX: "2560m"

  # Database Configuration (using variable names expected by application YAML)
  POSTGRES_URL: "************************************************"

  # Redis Configuration (using variable names expected by application YAML)
  REDIS_HOST: "redis-service"
  REDIS_PORT: "6379"

  # RabbitMQ Configuration (using variable names expected by application YAML)
  RABBITMQ_HOST: "rabbitmq-service"
  RABBITMQ_PORT: "5672"

  # OAuth2/Security Configuration (using variable names expected by application YAML)
  JWT_ISSUER_URI: "https://datapakt-demo.datapakt.ai/kc/realms/datapakt"
---
# Secret for sensitive configuration
apiVersion: v1
kind: Secret
metadata:
  name: datapakt-backend-secrets
  namespace: default
type: Opaque
data:
  # Database Credentials (base64 encoded)
  POSTGRES_USERNAME: cG9zdGdyZXM=  # postgres
  POSTGRES_PASSWORD: cGFzc3dvcmQ=  # password

  # RabbitMQ Credentials (base64 encoded)
  RABBITMQ_USERNAME: dXNlcg==  # user
  RABBITMQ_PASSWORD: cGFzc3dvcmQ=  # password

  # Text Encryption Service (base64 encoded)
  TEXT_ENCRYPTION_KEY: YW55LWtleQ==

---
# Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: datapakt-backend
  namespace: default
  labels:
    app: datapakt-backend
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: datapakt-backend
  template:
    metadata:
      labels:
        app: datapakt-backend
        version: v1
    spec:
      imagePullSecrets:
        - name: ghcr-secret
      containers:
        - name: datapakt-backend
          image: ghcr.io/datapakt/datapakt-backend:staging
          imagePullPolicy: Always
          ports:
            - containerPort: 8080
              name: http
          envFrom:
            - configMapRef:
                name: datapakt-backend-config
            - secretRef:
                name: datapakt-backend-secrets
          resources:
            requests:
              memory: "1Gi"
              cpu: "500m"
            limits:
              memory: "3Gi"
              cpu: "2"
          livenessProbe:
            httpGet:
              path: /api/actuator/health
              port: 8080
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /api/actuator/health/readiness
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          startupProbe:
            httpGet:
              path: /api/actuator/health
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 30

---
# Service
apiVersion: v1
kind: Service
metadata:
  name: datapakt-backend-service
  namespace: default
  labels:
    app: datapakt-backend
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: 8080
      protocol: TCP
      name: http
  selector:
    app: datapakt-backend
